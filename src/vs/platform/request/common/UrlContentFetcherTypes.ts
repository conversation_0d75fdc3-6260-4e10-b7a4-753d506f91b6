import { CancellationToken } from '../../../base/common/cancellation.js';


export enum UrlContentFetchChannelCommand {
	URL_TO_MARKDOWN = "urlToMarkdown",
}

export const URL_CONTENT_FETCHER_CHANNEL_NAME = "urlContentFetcherChannel"

export interface IRequestOptions {

	/**
	 * 设置请求的cookies
	 */
	cookies?: ICookieConfig[];

	/**
	 * 取消令牌
	 */
	token?: CancellationToken;

	/**
	 * 请求超时时间(毫秒)
	 */
	timeout?: number;

	/**
	 * 是否保持cookie状态
	 * 如果为true，则后续请求会保留当前请求设置的cookies
	 */
	persistCookies?: boolean;
}

// Cookie设置接口
export interface ICookieConfig {
	/** Cookie名称 */
	name: string;
	/** Cookie值 */
	value: string;
	/** Cookie所属域名 */
	domain?: string;
	/** Cookie路径 */
	path?: string;
	/** 是否仅通过HTTPS发送 */
	secure?: boolean;
	/** 是否仅HTTP请求可访问(不允许JS访问) */
	httpOnly?: boolean;
	/** Cookie过期时间(UTC) */
	expirationDate?: number;
	/** Same site策略 */
	sameSite?: 'unspecified' | 'no_restriction' | 'lax' | 'strict';
}
