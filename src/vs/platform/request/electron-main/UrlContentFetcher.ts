
import { BrowserWindow } from 'electron';
import TurndownService from "turndown"
import { createDecorator } from '../../instantiation/common/instantiation.js';
import pWaitFor from '../../../workbench/common/pWaitFor.js';
import { Disposable } from '../../../base/common/lifecycle.js';
import { ICookieConfig, IRequestOptions } from '../common/UrlContentFetcherTypes.js';

// 创建服务标识符
export const IHeadlessBrowserService = createDecorator<IHeadlessBrowserService>('headlessBrowserService');

// 定义服务接口
export interface IHeadlessBrowserService {
	/**
	 * 获取指定URL的HTML内容，并清理指定标签
	 * @param url 目标网页URL
	 * @param options 请求选项，包含headers等配置
	 * @returns 处理后的HTML内容
	 */
	urlToMarkdown(url: string, options?: IRequestOptions): Promise<string>;
}

// 实现服务
export class HeadlessBrowserService extends Disposable implements IHeadlessBrowserService {
	private browser: Electron.BrowserWindow | null = null;
	private createBrowser(): void {
		this.browser = new BrowserWindow({
			show: false, // 隐藏窗口，实现无头
			webPreferences: {
				offscreen: true, // 启用离屏渲染
				nodeIntegration: false,
				contextIsolation: true,
				webSecurity: true,
				allowRunningInsecureContent: false
			}
		});
	}

	public async urlToMarkdown(url: string, options?: IRequestOptions): Promise<string> {
		if (!this.browser) {
			this.createBrowser();
		}

		try {
			// 如果不需要持久化cookies，则先清除
			if (options?.persistCookies !== true) {
				await this.clearCookies();
			}

			// 设置cookies（如果有）
			if (options?.cookies && options.cookies.length > 0) {
				await this.setCookies(url, options.cookies);
			}

			// 加载URL
			await this.browser!.loadURL(url);

			await pWaitFor(() => { return this.browser?.webContents.isLoading() === false }, { interval: 1000, timeout: 60_000 })

			// 使用页面内执行的JavaScript获取HTML并清理指定标签
			const html = await this.browser!.webContents.executeJavaScript(`
                (function() {
                    // 创建一个新的DOM解析器和文档
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(document.documentElement.outerHTML, 'text/html');

                    // 要删除的标签列表
                    const tagsToRemove = ['script', 'style', 'nav', 'footer', 'header'];

                    // 删除指定标签
                    tagsToRemove.forEach(tag => {
                        const elements = doc.querySelectorAll(tag);
                        elements.forEach(el => el.parentNode.removeChild(el));
                    });

                    // 返回清理后的HTML
                    return doc.documentElement.outerHTML;
                })();
            `, false);


			const turndownService = new TurndownService()
			return turndownService.turndown(html)
		} catch (error) {
			console.error('Failed to fetch clean HTML:', error);
			throw error;
		}
	}

	private async setCookies(url: string, cookies: ICookieConfig[]) {
		if (!this.browser) {
			throw new Error('Browser instance not available');
		}
		const session = this.browser.webContents.session;
		const parsedUrl = new URL(url);
		const domain = parsedUrl.hostname;
		const cookiePromises = cookies.map(cookie => {
			const cookieConfig: Electron.CookiesSetDetails = {
				url,
				name: cookie.name,
				value: cookie.value,
				domain: cookie.domain || domain,
				path: cookie.path || '/',
				secure: cookie.secure !== undefined ? cookie.secure : false,
				httpOnly: cookie.httpOnly !== undefined ? cookie.httpOnly : false,
				expirationDate: cookie.expirationDate
			};

			if (cookie.sameSite) {
				cookieConfig.sameSite = cookie.sameSite as any;
			}

			return session.cookies.set(cookieConfig);
		});
		await Promise.all(cookiePromises);
	}
	public async getCookies(url?: string): Promise<Electron.Cookie[]> {
		if (!this.browser) {
			throw new Error('Browser instance not available');
		}

		const session = this.browser.webContents.session;
		const filter: Electron.CookiesGetFilter = {};

		if (url) {
			filter.url = url;
		}

		return session.cookies.get(filter);
	}

	public async clearCookies(url?: string): Promise<void> {
		if (!this.browser) {
			return;
		}

		const session = this.browser.webContents.session;

		if (url) {
			// 只清除特定URL的cookies
			const cookies = await this.getCookies(url);

			const clearPromises = cookies.map(cookie =>
				session.cookies.remove(url, cookie.name)
			);

			await Promise.all(clearPromises);
		} else {
			// 清除所有cookies
			await session.clearStorageData({
				storages: ['cookies']
			});
		}
	}


	public override dispose(): void {
		super.dispose()

		if (this.browser) {
			this.browser.close();
			this.browser = null;
		}
	}
}
