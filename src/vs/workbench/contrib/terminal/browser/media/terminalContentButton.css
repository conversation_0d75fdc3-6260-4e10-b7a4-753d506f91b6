/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.terminal-content-button-container {
	position: absolute;
	top: 10px;
	right: 30px; /* 调整右侧距离，避免挡住滚动条 */
	z-index: 100;
}

.terminal-content-button {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 8px;
	height: 24px;
	border-radius: 4px;
	background-color: var(--vscode-button-secondaryBackground);
	color: var(--vscode-button-secondaryForeground);
	cursor: pointer;
	opacity: 0.7;
	transition: opacity 0.2s ease-in-out;
}

.terminal-content-button-text {
	margin-left: 5px;
	font-size: 12px;
}

.terminal-content-button:hover {
	opacity: 1;
	background-color: var(--vscode-button-secondaryHoverBackground);
}
