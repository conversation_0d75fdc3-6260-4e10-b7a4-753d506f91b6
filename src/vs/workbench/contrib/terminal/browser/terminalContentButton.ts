/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as dom from '../../../../base/browser/dom.js';
import { Codicon } from '../../../../base/common/codicons.js';
import { DisposableStore, IDisposable } from '../../../../base/common/lifecycle.js';
import { localize } from '../../../../nls.js';
import { ICommandService } from '../../../../platform/commands/common/commands.js';
import { INotificationService } from '../../../../platform/notification/common/notification.js';
import { ICommandDetectionCapability, TerminalCapability } from '../../../../platform/terminal/common/capabilities/capabilities.js';
import { registerIcon } from '../../../../platform/theme/common/iconRegistry.js';
import { IChatThreadService } from '../../codeseek/browser/chatThreadService.js';
import { CODESEEK_OPEN_SIDEBAR_ACTION_ID } from '../../codeseek/browser/sidebarActions.js';
import { ISidebarStateService } from '../../codeseek/browser/sidebarStateService.js';
import { TerminalSelection } from '../../codeseek/common/selectedFileService.js';
import './media/terminalContentButton.css';
import { ITerminalGroupService, ITerminalInstance } from './terminal.js';

// 注册显示终端内容按钮的图标
export const showTerminalContentIcon = registerIcon('terminal-show-content', Codicon.clippy, localize('terminalShowContentIcon', 'Icon for showing terminal content in a notification.'));

// 用于创建并管理"显示终端内容"按钮的类
export class TerminalContentButton implements IDisposable {
	// 静态实例跟踪器，用于在不同组件之间访问
	private static _currentInstance: TerminalContentButton | undefined;

	private readonly _disposables = new DisposableStore();
	private _container: HTMLElement | undefined;
	private _button: HTMLElement | undefined;
	private _buttonText: HTMLSpanElement | undefined;

	constructor(
		private readonly _parentElement: HTMLElement,
		@INotificationService private readonly _notificationService: INotificationService,
		@ITerminalGroupService private readonly _terminalGroupService: ITerminalGroupService,
		@IChatThreadService private readonly _chatThreadService: IChatThreadService,
		@ISidebarStateService private readonly _sidebarStateService: ISidebarStateService,
		@ICommandService private readonly _commandService: ICommandService
	) {
		// 将当前实例保存到静态变量中
		TerminalContentButton._currentInstance = this;
		this._createButton();

		// 更新按钮上的快捷键显示
		this._updateKeybindingLabel();
	}

	// 静态方法，用于获取当前实例
	public static getCurrentInstance(): TerminalContentButton | undefined {
		return TerminalContentButton._currentInstance;
	}

	// 创建按钮
	private _createButton(): void {
		// 创建一个容器元素
		this._container = document.createElement('div');
		this._container.className = 'terminal-content-button-container';
		this._parentElement.appendChild(this._container);

		// 创建按钮元素
		this._button = document.createElement('div');
		this._button.className = `terminal-content-button codicon ${showTerminalContentIcon.id}`;

		// 创建文字标签元素，并保存引用以便后续更新
		this._buttonText = document.createElement('span');
		this._buttonText.className = 'terminal-content-button-text';
		this._button.appendChild(this._buttonText);

		this._container.appendChild(this._button);

		// 初始化按钮的文本和提示内容
		this._updateKeybindingLabel();

		// 添加点击事件监听器
		this._disposables.add(dom.addDisposableListener(this._button, dom.EventType.CLICK, () => this._showTerminalContent()));
	}

	// 更新按钮上显示的键绑定标签
	private _updateKeybindingLabel(): void {
		if (!this._button || !this._buttonText) {
			return;
		}
		// 更新按钮提示
		this._button.title = localize('terminal.showContent', "将终端内容添加到聊天");
		this._buttonText.textContent = localize('terminal.showContentLabel', "添加终端内容到聊天");
	}

	// 显示终端内容并将其添加到聊天中
	private async _showTerminalContent(instance?: ITerminalInstance, maxCommands: number = 1): Promise<void> {
		// 获取活动终端实例
		const activeInstance = instance || this._terminalGroupService.activeInstance;
		if (!activeInstance) {
			this._notificationService.info(localize('terminal.noActiveInstance', "没有活动的终端实例"));
			return;
		}

		try {
			// 获取终端内容
			const content = await this._getTerminalBufferContent(activeInstance, maxCommands);
			if (!content || content.trim() === '') {
				this._notificationService.info(localize('terminal.noContent', "终端没有内容可以添加到聊天中"));
				return;
			}

			// 创建一个包含终端内容的对象，作为选择项传递给聊天面板
			const terminalSelection: TerminalSelection = {
				type: 'Terminal',
				content: content,
				title: '终端内容',
				fileURI: activeInstance.resource,
				selectionStr: content,
				range: null,
				timestamp: Date.now(),
				fromMention: false
			};

			// 检查聊天面板是否已打开，如果没有则打开它
			if (!this._sidebarStateService.isSidebarChatOpen()) {
				await this._commandService.executeCommand(CODESEEK_OPEN_SIDEBAR_ACTION_ID);
			}

			// 将终端内容添加到聊天中
			this._chatThreadService.addSelectionToChat(terminalSelection);

			// 显示成功提示
			this._notificationService.info(localize('terminal.contentAddedToChat', "终端内容已添加到聊天面板"));
		} catch (error) {
			console.error('处理终端内容时出错', error);
			this._notificationService.error(localize('terminal.processingError', "处理终端内容时出错"));
		}
	}

	// 获取终端缓冲区内容的辅助方法
	private async _getTerminalBufferContent(instance?: ITerminalInstance, maxCommands: number = 3): Promise<string> {
		if (!instance || !instance.xterm) {
			return localize('terminal.noInstance', "没有活动的终端实例");
		}

		try {
			// 首先检查终端是否支持命令检测能力
			const commandDetection = instance.capabilities.get(TerminalCapability.CommandDetection);

			// 如果终端支持命令检测能力，则使用该能力获取命令会话
			if (commandDetection) {
				return this._getContentFromCommandDetection(commandDetection, instance, maxCommands);
			}
			// 如果不支持命令检测能力，则使用旧方法解析缓冲区
			else {
				return this._getContentFromBufferParsing(instance);
			}
		} catch (error) {
			console.error('获取终端内容失败', error);
			return localize('terminal.contentError', "获取终端内容时出错");
		}
	}

	// 使用命令检测能力获取终端内容
	private _getContentFromCommandDetection(commandDetection: ICommandDetectionCapability, instance: ITerminalInstance, maxCommands: number = 3): string {
		// 获取已执行的命令列表
		const commands = commandDetection.commands;

		// 如果没有命令，返回提示信息
		if (commands.length === 0) {
			return localize('terminal.noCommands', "终端没有检测到任何命令");
		}

		// 获取更多命令历史，根据maxCommands参数获取最后N个命令
		const commandsToGet = Math.min(maxCommands, commands.length);
		const recentCommands = commands.slice(-commandsToGet);

		// 构建包含命令和输出的结果
		const result: string[] = [];

		// 添加会话信息头部
		result.push("--- 终端会话内容---");
		result.push(`会话时间: ${new Date().toLocaleString()}`);
		result.push(`终端ID: ${instance.instanceId}`);

		// 添加当前工作目录信息（如果有）
		if (commandDetection.cwd) {
			result.push(`当前目录: ${commandDetection.cwd}`);
		}
		result.push('');

		// 遍历最近的命令，收集命令文本和输出
		recentCommands.forEach((cmd, index) => {
			// 添加命令分隔符
			if (index === 0) {
				result.push('=== 最近命令会话 ===\n');

			}
			result.push('\n=== 命令会话 ' + (index + 1) + ' ===\n');

			// 添加时间戳信息
			const date = new Date(cmd.timestamp);
			const timeString = date.toLocaleString();

			// 添加命令文本和执行信息
			result.push(`$ ${cmd.command}`);
			result.push(`执行时间: ${timeString}`);

			// 添加执行时长（如果命令时长超过1秒）
			if (cmd.duration > 1000) {
				const durationSeconds = (cmd.duration / 1000).toFixed(1);
				result.push(`执行耗时: ${durationSeconds}秒`);
			}

			// 添加退出码信息（如果有）
			if (cmd.exitCode !== undefined) {
				const exitStatus = cmd.exitCode === 0 ? '成功' : '失败';
				result.push(`执行状态: ${exitStatus} (退出码: ${cmd.exitCode})`);
			}

			// 添加命令输出（如果有）
			result.push('\n输出:');
			const output = cmd.getOutput();
			if (output) {
				result.push(output);
			} else if (cmd.hasOutput()) {
				result.push('(输出内容无法获取)');
			} else {
				result.push('(无输出)');
			}
		});

		// 检查当前是否正在执行命令
		const currentCommand = commandDetection.executingCommandObject;
		if (currentCommand) {
			result.push('\n=== 当前执行中的命令 ===\n');
			result.push(`$ ${currentCommand.command}`);

			// 显示执行时间
			if (currentCommand.timestamp) {
				const startTime = new Date(currentCommand.timestamp);
				const timeString = startTime.toLocaleString();
				result.push(`开始执行时间: ${timeString}`);

				// 计算当前命令已执行的时长
				const now = Date.now();
				const elapsedSeconds = ((now - currentCommand.timestamp) / 1000).toFixed(1);
				result.push(`已执行: ${elapsedSeconds}秒`);
			}

			// 尝试获取部分输出（如果有）
			result.push('\n当前输出:');
			if (currentCommand.getOutput && typeof currentCommand.getOutput === 'function') {
				const partialOutput = currentCommand.getOutput();
				if (partialOutput) {
					result.push(partialOutput);
				} else {
					result.push('(尚无输出)');
				}
			} else {
				result.push('(无法获取输出)');
			}
		}

		return result.join('\n');
	}

	// 使用缓冲区解析方法获取终端内容（旧方法，作为后备方案）
	private _getContentFromBufferParsing(instance: ITerminalInstance): string {
		// 确保xterm和buffer都存在
		if (!instance?.xterm?.raw?.buffer?.active) {
			return localize('terminal.noTerminalBuffer', "无法访问终端缓冲区");
		}

		const xterm = instance.xterm;
		const buffer = xterm.raw.buffer.active;
		const lineCount = buffer.length;
		const lines: string[] = [];

		// 记录最后三个提示符的位置
		const promptLines: number[] = [];

		// 从缓冲区中查找最后三个命令提示符
		for (let i = lineCount - 1; i >= 0; i--) {
			const line = buffer.getLine(i);
			if (line) {
				const lineContent = line.translateToString(true);
				// 检测常见的提示符模式，例如 '$ ', '> ', 'user@host:~$ ' 等
				// bash, zsh, fish 等常见shell的提示符通常以这些字符结尾
				if (lineContent.match(/[@:~#\$>]\s*$/)) {
					promptLines.push(i);
					if (promptLines.length >= 3) {
						break; // 找到三个提示符就可以停止了
					}
				}
			}
		}

		// 确定起始行和结束行
		let startLine = 0;
		let endLine = lineCount - 1;

		// 如果找到了三个提示符，则收集倒数第三个提示符到倒数第一个提示符之间的所有内容
		if (promptLines.length >= 3) {
			startLine = promptLines[2]; // 倒数第三个提示符
			endLine = lineCount - 1; // 直到缓冲区最后一行
		}
		// 如果只找到两个提示符，则从倒数第二个提示符到最后
		else if (promptLines.length >= 2) {
			startLine = promptLines[1]; // 倒数第二个提示符
			endLine = lineCount - 1; // 直到缓冲区最后一行
		}
		// 如果只找到一个提示符或没找到，则返回所有内容
		else {
			startLine = 0;
			endLine = lineCount - 1;
		}

		// 收集内容
		for (let i = startLine; i <= endLine; i++) {
			const line = buffer.getLine(i);
			if (line) {
				lines.push(line.translateToString(true));
			}
		}

		// 如果没有内容（可能是因为终端刚打开），返回提示信息
		if (lines.length === 0) {
			return localize('terminal.noContent', "没有找到有效的终端会话内容");
		}

		// 增加一些元信息，使输出更有用
		const result: string[] = [];
		result.push("--- 终端会话内容（通过缓冲区解析获取）---");
		result.push(`会话时间: ${new Date().toLocaleString()}`);
		result.push(`终端ID: ${instance.instanceId}`);
		result.push("");
		result.push(lines.join('\n').trim());

		return result.join('\n');
	}

	// 获取实例方法，用于在外部调用
	public async getContentFromInstance(instance: ITerminalInstance): Promise<void> {
		return this._showTerminalContent(instance, 1);
	}

	// 更新按钮在页面中的位置
	public layout(): void {
		// 可以在此处添加布局逻辑
	}

	// 资源释放
	public dispose(): void {
		// 如果当前静态实例引用指向此实例，则清除引用
		if (TerminalContentButton._currentInstance === this) {
			TerminalContentButton._currentInstance = undefined;
		}

		this._disposables.dispose();
		this._container?.remove();
		this._container = undefined;
		this._button = undefined;
		this._buttonText = undefined;
	}
}
