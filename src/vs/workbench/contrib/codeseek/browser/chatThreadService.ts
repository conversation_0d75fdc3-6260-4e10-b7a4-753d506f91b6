/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { Disposable } from '../../../../base/common/lifecycle.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { IStorageService, StorageScope, StorageTarget } from '../../../../platform/storage/common/storage.js';

import { Emitter, Event } from '../../../../base/common/event.js';
import { generateUuid } from '../../../../base/common/uuid.js';
import { ICommandService } from '../../../../platform/commands/common/commands.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import pWaitFor from '../../../common/pWaitFor.js';
import { IEditorService } from '../../../services/editor/common/editorService.js';
import { ICodeSeekExporerService } from '../common/codeseekExporerService.js';
import { ICodeseekFileService } from '../common/codeseekFileService.js';
import { ChatMode, ICodeseekSettingsService } from '../common/codeseekSettingsService.js';
import { ILLMMessageService } from '../common/llmMessageService.js';
import { SendLLMType, toLLMChatMessage, ToolCallResultCode, ToolCallResultType, ToolCallType } from '../common/llmMessageTypes.js';
import { CodebaseSelection, IMentionService, StagingSelectionItem } from '../common/selectedFileService.js';
import { ApproveRequestResultType, AskReponseType, AskResponse, InternalToolInfo, ToolCallReturnType, ToolName, ToolNameEnum } from '../common/toolsServiceTypes.js';
import { IToolsService } from '../common/toolsService.js';
import { ICodeseekTaskService } from './codeseekAgentTaskService.js';
import { ICodeseekCodeSelectionService } from './codeseekCodeSelectionService.js';
import { chat_systemMessage, chat_userMessageContent, fix_systemMessage, messageExpansion_systemMessage, user_rules } from './../common/prompt/prompts.js';
import { CODESEEK_ADD_SELECTION_TO_SIDEBAR_ACTION_ID, CODESEEK_OPEN_SIDEBAR_ACTION_ID } from './sidebarActions.js';
import { ISidebarStateService } from './sidebarStateService.js';
import { ICodebaseSearchService } from './codebaseSearchService.js';
import { IModelService } from '../../../../editor/common/services/model.js';
import { FeatureNames } from '../common/codeseekSettingsTypes.js';
import { ICodeseekLogger } from '../common/codeseekLogService.js';
import { THREAD_MESSAGES_STORAGE_KEY, THREAD_ABSTRACT_STORAGE_KEY } from '../common/storageKeys.js';
import { URI } from '../../../../base/common/uri.js';
import { LINE_BREAK_REGEX } from './../common/prompt/tags.js';
import { TaskInfo } from '../../../api/browser/mainThreadCodeSeekTaskService.js';
import { IMetricsService, METRICS_EVENT } from '../common/metricsService.js';
import { INotificationService } from '../../../../platform/notification/common/notification.js';
import { ICodeseekUacLoginService } from '../common/uac/codeseekUacLoginService.js';
import { IUrlContentFetcherService } from '../common/IUrlContentFetchService.js';

const findLastIndex = <T>(arr: T[], condition: (t: T) => boolean): number => {
	for (let i = arr.length - 1; i >= 0; i--) {
		if (condition(arr[i])) {
			return i;
		}
	}
	return -1;
};

export type CodeSeekAsk =
	| 'followup'
	| 'command'
	| 'command_output'
	| 'completion_result'
	| 'tool'
	| 'api_req_failed'
	| 'resume_task'
	| 'resume_completed_task'
	| 'mistake_limit_reached'
	| 'browser_action_launch'
	| 'use_mcp_server';

export type ClineSay =
	| 'task'
	| 'error'
	| 'api_req_started'
	| 'api_req_finished'
	| 'api_req_retried'
	| 'api_req_retry_delayed'
	| 'api_req_deleted'
	| 'text'
	| 'reasoning'
	| 'completion_result'
	| 'user_feedback'
	| 'user_feedback_diff'
	| 'command_output'
	| 'tool'
	| 'shell_integration_warning'
	| 'browser_action'
	| 'browser_action_result'
	| 'command'
	| 'mcp_server_request_started'
	| 'mcp_server_response'
	| 'new_task_started'
	| 'new_task'
	| 'checkpoint_saved'
	| 'rooignore_error';



export type userMessageOpts = {
	from: 'Fix' | 'Chat';
	userMessage: string;
	linterErrors?: string;
};

export type ToolMessageType = 'ask' | 'say';
export type ToolMessage<T extends ToolName> = {
	role: 'tool';
	content: string | undefined; // result
	result: ToolCallReturnType[T] | null; // text message of result
} & ToolCallType;

export type AskMessage = {
	type: string;
	content: ToolCallType | null; // content displayed to the LLM on future calls - allowed to be '', will be replaced with (empty)
	// operate: string | null; // content displayed to user  - allowed to be '', will be ignored
};

// WARNING: changing this format is a big deal!!!!!! need to migrate old format to new format on users' computers so people don't get errors.
export type ChatMessage =
	| {
		role: 'system';
		content: string;
		displayContent?: undefined;
	} | {
		role: 'user';
		content: string | null; // content displayed to the LLM on future calls - allowed to be '', will be replaced with (empty)
		displayContent: string | null; // content displayed to user  - allowed to be '', will be ignored
		state: {
			stagingSelections: StagingSelectionItem[];
			isBeingEdited: boolean;
		};
	} | {
		role: 'assistant';
		content: string | null; // content received from LLM  - allowed to be '', will be replaced with (empty)
		displayContent: string | null; // content displayed to user (this is the same as content for now) - allowed to be '', will be ignored
	}
	| ToolMessage<ToolName>;

type UserMessageType = ChatMessage & { role: 'user' };
type UserMessageState = UserMessageType['state'];

export const defaultMessageState: UserMessageState = {
	stagingSelections: [],
	isBeingEdited: false,
};

// a 'thread' means a chat message history
export type ChatThreads = {
	[id: string]: {
		id: string; // store the id here too
		createdAt: string; // ISO string
		lastModified: string; // ISO string
		messagesLength: number;
		firstUserMessage: string;
		state: {
			stateSelections: StateSelections;
			focusedMessageIdx: number | undefined; // index of the message that is being edited (undefined if none)
			isCheckedOfSelectionId: { [selectionId: string]: boolean };
			askMessage?: AskMessage;
			askResponse?: AskResponse;
			askResponseText?: string;
		};
	};
};


export type StateSelections = {
	list: StagingSelectionItem[];
	followEditorActive: boolean;
};
type ThreadType = ChatThreads[string];


export type ThreadsState = {
	allThreads: ChatThreads;
	currentThreadId: string; // intended for internal use only
	currentThreadMessages: ChatMessage[];
};

export type ThreadStreamState = {
	[threadId: string]: undefined | {
		error?: { message: string; fullError: Error | null };
		messageSoFar?: string;
		streamingToken?: string;
		toolCall?: ToolCallType & { result: ToolCallReturnType[ToolName] | null; content: string };
		isStreaming?: boolean;
	};
};


const newThreadObject = () => {
	const now = new Date().toISOString();
	return {
		id: generateUuid(),
		createdAt: now,
		lastModified: now,
		messagesLength: 0,
		firstUserMessage: '',
		state: {
			stateSelections: { list: [], followEditorActive: true },
			focusedMessageIdx: undefined,
			isCheckedOfSelectionId: {}
		},

	} satisfies ChatThreads[string];
};


export interface IChatThreadService {
	readonly _serviceBrand: undefined;

	readonly state: ThreadsState;
	readonly streamState: ThreadStreamState;

	onDidChangeCurrentThread: Event<void>;
	onDidChangeStreamState: Event<{ threadId: string }>;

	isCurrentThreadWorking(): boolean;
	getCurrentThread(): ChatThreads[string];
	getCurrentThreadMessages(): ChatMessage[];
	openNewThread(): void;
	switchToThread(threadId: string): void;
	deleteThread(threadId: string): void;

	// you can edit multiple messages
	// the one you're currently editing is "focused", and we add items to that one when you press cmd+L.
	getFocusedMessageIdx(): number | undefined;
	isFocusingMessage(): boolean;
	setFocusedMessageIdx(messageIdx: number | undefined): void;

	// exposed getters/setters
	getCurrentMessageState: (messageIdx: number) => UserMessageState;
	setCurrentMessageState: (messageIdx: number, newState: Partial<UserMessageState>) => void;
	getCurrentThreadStagingSelections: () => StagingSelectionItem[];
	setCurrentThreadStagingSelections: (stagingSelections: StagingSelectionItem[]) => void;
	getCurrentThreadStateSelections: () => StateSelections;
	setCurrentThreadStateSelectionsChangeSelections: () => void;

	sendMessage(message: string, chatMode: ChatMode, taskParams?: Record<string, any>): Promise<void>;
	// call to edit a message
	editUserMessageAndStreamResponse({ userMessage, chatMode, messageIdx }: { userMessage: string; chatMode: ChatMode; messageIdx: number }): Promise<void>;

	// call to add a message
	addUserMessageAndStreamResponse({ userMessageOpts, chatMode, chatSelections, agentParamsFromPlugin, taskInfo }:
		{
			userMessageOpts: userMessageOpts;
			chatMode: ChatMode;
			chatSelections?: { prevSelns?: StagingSelectionItem[]; currSelns?: StagingSelectionItem[] };
			agentParamsFromPlugin?: Record<string, any>;
			taskInfo?: TaskInfo;
		}):
		Promise<void>;

	cancelStreaming(threadId: string): void;
	dismissStreamError(threadId: string): void;

	setAskResponse(askResponse: AskResponse): void;

	addSelectionToChat(selection?: StagingSelectionItem): void;
}

export const IChatThreadService = createDecorator<IChatThreadService>('codeseekChatThreadService');
class ChatThreadService extends Disposable implements IChatThreadService {
	_serviceBrand: undefined;

	// this fires when the current thread changes at all (a switch of currentThread, or a message added to it, etc)
	private readonly _onDidChangeCurrentThread = new Emitter<void>();
	readonly onDidChangeCurrentThread: Event<void> = this._onDidChangeCurrentThread.event;

	readonly streamState: ThreadStreamState = {};
	private readonly _onDidChangeStreamState = new Emitter<{ threadId: string }>();
	readonly onDidChangeStreamState: Event<{ threadId: string }> = this._onDidChangeStreamState.event;

	private readonly _onNewThreadOpened = new Emitter<void>();
	readonly onNewThreadOpened: Event<void> = this._onNewThreadOpened.event;

	state: ThreadsState; // allThreads is persisted, currentThread is not

	constructor(
		@IStorageService private readonly _storageService: IStorageService,
		@ICodeseekFileService private readonly _codeseekFileService: ICodeseekFileService,
		@ILLMMessageService private readonly _llmMessageService: ILLMMessageService,
		@IToolsService private readonly _toolsService: IToolsService,
		@IWorkspaceContextService private readonly _workspaceContextService: IWorkspaceContextService,
		@ICodeSeekExporerService private readonly _codeSeekExporerService: ICodeSeekExporerService,
		@IModelService private readonly _modelService: IModelService,
		@ICodeseekTaskService private readonly _codeseekTaskService: ICodeseekTaskService,
		@ISidebarStateService private readonly _sidebarStateService: ISidebarStateService,
		@ICommandService private readonly _commandService: ICommandService,
		@ICodeseekCodeSelectionService private readonly _codeSeekCodeSelectionService: ICodeseekCodeSelectionService,
		@IEditorService private readonly _editorService: IEditorService,
		@ICodeseekSettingsService private readonly _codeseekSettingsService: ICodeseekSettingsService,
		@ICodeseekLogger private readonly _codeseekLogService: ICodeseekLogger,
		@ICodebaseSearchService private readonly _codebaseSearchService: ICodebaseSearchService,
		@IMetricsService private readonly _metricsService: IMetricsService,
		@IMentionService private readonly _mentionsService: IMentionService,
		@IUrlContentFetcherService private readonly _urlContentFetcher: IUrlContentFetcherService,
		@INotificationService private readonly _notificationService: INotificationService,
		@ICodeseekUacLoginService private readonly _zteUserInfoService: ICodeseekUacLoginService,
	) {
		super();

		this.state = { allThreads: {}, currentThreadId: null as unknown as string, currentThreadMessages: [] }; // default state

		const readThreads = this._readAllThreads() || {};

		const allThreads = readThreads;
		this.state = {
			allThreads: allThreads,
			currentThreadId: null as unknown as string, // gets set in startNewThread()
			currentThreadMessages: []
		};

		// always be in a thread
		this.openNewThread();

		this._editorService.onDidActiveEditorChange(() => this.onDidActiveEditorChange());
		this._codeSeekCodeSelectionService.onDidAddContentFromEditor(selection => this.addSelectionToChat(selection));
	}

	setAskResponse(askResponse: AskResponse): void {
		const threadId = this.getCurrentThread().id;
		const currentThreadState = this.state.allThreads[threadId].state;
		currentThreadState.askResponse = { type: askResponse.type, response: askResponse.response, text: askResponse.text };
	}

	private _convertThreadDataFromStorage(threadsStr: string): ChatThreads | ChatMessage[] {
		return JSON.parse(threadsStr, (key, value) => {
			if (value && typeof value === 'object' && value.$mid === 1) {
				return URI.from(value);
			}
			return value;
		});
	}

	private _readAllThreads(): ChatThreads | null {
		const threadsStr = this._storageService.get(THREAD_ABSTRACT_STORAGE_KEY, StorageScope.WORKSPACE);
		if (!threadsStr) {
			return null;
		}
		const threads = this._convertThreadDataFromStorage(threadsStr);

		return threads as ChatThreads;
	}

	private _storeChatSummary() {
		const { allThreads } = this.state
		const serializedSummary = JSON.stringify(allThreads);
		this._storageService.store(
			THREAD_ABSTRACT_STORAGE_KEY,
			serializedSummary,
			StorageScope.WORKSPACE,
			StorageTarget.USER
		);
	}
	private _storeChatMessages(threadId: string, messags: ChatMessage[]) {
		const serializedMessages = JSON.stringify(messags)

		this._storageService.store(
			`${THREAD_MESSAGES_STORAGE_KEY}-${threadId}`,
			serializedMessages,
			StorageScope.WORKSPACE,
			StorageTarget.USER
		);
	}
	private _loadChatMessages(threadId: string): ChatMessage[] {
		const messagesStr = this._storageService.get(`${THREAD_MESSAGES_STORAGE_KEY}-${threadId}`, StorageScope.WORKSPACE);
		if (!messagesStr) {
			return [];
		}
		const messages = this._convertThreadDataFromStorage(messagesStr);

		return messages as ChatMessage[];
	}
	private _deleteThreadMessages(threadId: string): void {
		this._storageService.remove(`${THREAD_MESSAGES_STORAGE_KEY}-${threadId}`, StorageScope.WORKSPACE)
	}
	// this should be the only place this.state = ... appears besides constructor
	private _setState(state: Partial<ThreadsState>, affectsCurrent: boolean) {
		this.state = {
			...this.state,
			...state
		};
		if (affectsCurrent)
			this._onDidChangeCurrentThread.fire();
	}

	private _getSelectionsUpToMessageIdx(messageIdx: number) {
		const prevMessages = this.state.currentThreadMessages.slice(0, messageIdx);
		return prevMessages.flatMap(m => m.role === 'user' && m.state.stagingSelections || []);
	}

	private _setStreamState(threadId: string, state: Partial<NonNullable<ThreadStreamState[string]>>) {
		this.streamState[threadId] = {
			...this.streamState[threadId],
			...state
		};
		this._onDidChangeStreamState.fire({ threadId });
	}

	// ---------- streaming ----------
	private _finishStreamingTextMessage = (threadId: string, content: string, error?: { message: string; fullError: Error | null }) => {
		// add assistant's message to chat history, and clear selection
		this._addMessageToThread(threadId, { role: 'assistant', content, displayContent: content || null });
		this._setStreamState(threadId, { messageSoFar: undefined, streamingToken: undefined, error, isStreaming: false });
	};

	async sendMessage(message: string, chatMode: ChatMode, agentParamsFromPlugin?: Record<string, any>): Promise<void> {
		if (this.isCurrentThreadWorking()) {
			console.log('chat is working, please wait');
			return;
		}

		if (!this._sidebarStateService.isSidebarChatOpen()) {
			await this._commandService.executeCommand(CODESEEK_OPEN_SIDEBAR_ACTION_ID);
		}
		await this._commandService.executeCommand(CODESEEK_ADD_SELECTION_TO_SIDEBAR_ACTION_ID);
		this.addUserMessageAndStreamResponse({ userMessageOpts: { from: 'Chat', userMessage: message }, chatMode: chatMode, agentParamsFromPlugin });
	}

	async editUserMessageAndStreamResponse({ userMessage, chatMode, messageIdx }: { userMessage: string; chatMode: ChatMode; messageIdx: number }) {

		const currentThreadMessages = this.state.currentThreadMessages

		if (currentThreadMessages[messageIdx]?.role !== 'user') {
			throw new Error("Error: editing a message with role !=='user'");
		}

		// get prev and curr selections before clearing the message
		const prevSelns = this._getSelectionsUpToMessageIdx(messageIdx);
		const currSelns = currentThreadMessages[messageIdx].state.stagingSelections || [];

		// clear messages up to the index
		const slicedMessages = currentThreadMessages.slice(0, messageIdx);
		this._setState({
			currentThreadMessages: slicedMessages
		}, true);

		// re-add the message and stream it
		this.addUserMessageAndStreamResponse({ userMessageOpts: { from: 'Chat', userMessage }, chatMode, chatSelections: { prevSelns, currSelns } });
	}

	async getCodeBaseSelections(userMessageOpts: userMessageOpts): Promise<CodebaseSelection[]> {
		if (userMessageOpts.from === 'Fix') {
			return [];
		}
		let expandedUserMessage = [userMessageOpts.userMessage];
		if (this._codeseekSettingsService.state.globalSettings.enableLocalCodeBase || this._codeseekSettingsService.state.globalSettings.enableRemoteCodeBase) {
			// expandedUserMessage = await this.expandUserMessage(userMessageOpts.userMessage, this.getCurrentThread().id);
			this._setStreamState(this.getCurrentThread().id, { streamingToken: undefined, isStreaming: true });
			const searchResult = await this._codebaseSearchService.semanticSearch(expandedUserMessage.join('\n'));
			this._codeseekLogService.info('Codebase search result:',
				JSON.stringify(searchResult, null, 2), `query: ${expandedUserMessage}`);
			const codebaseSelections: CodebaseSelection[] = searchResult.map(r => ({
				type: 'Codebase' as const,
				fileURI: r.uri,
				title: 'Codebase',
				selectionStr: r.content,
				range: r.range,
				fromMention: false
			}));
			return codebaseSelections;
		}
		return [];
	}

	async expandUserMessage(userMessage: string, threadId: string): Promise<string[]> {
		const messageExpansionResult: string[] = [userMessage];
		let res_: () => void;
		const awaitable = new Promise<void>((res, rej) => { res_ = res; });
		const expandedUserMessageToken = this._llmMessageService.sendLLMMessage({
			useProviderFor: FeatureNames.CtrlL,
			messagesType: 'chatMessages',
			messages: [{ role: 'system', content: messageExpansion_systemMessage }, { role: 'user', content: userMessage }],
			onText: () => { },
			onToolCall: () => { },
			onFinalMessage: (params) => {
				const result = params.fullText.trim();
				const queryMatch = result.match(/<expandedMessage>(.*?)<\/expandedMessage>/s);
				if (queryMatch && queryMatch[1]) {
					messageExpansionResult.push(...queryMatch[1].trim().split(LINE_BREAK_REGEX));
				}

				this._codeseekLogService.info(`messageExpansion, message expansion got result: ${messageExpansionResult}`);
				res_();
			},
			onError: (params) => {
				this._codeseekLogService.error('messageExpansion, message expansion call LLM error:', params.message);
				res_();
			},
			logging: { loggingName: 'messageExpansion' }
		});
		if (expandedUserMessageToken === null) return messageExpansionResult;
		this._setStreamState(threadId, { streamingToken: expandedUserMessageToken, isStreaming: true });

		await awaitable;
		return messageExpansionResult;
	}

	async addUserMessageAndStreamResponse({ userMessageOpts, chatMode, chatSelections, agentParamsFromPlugin, taskInfo }: {
		userMessageOpts: userMessageOpts;
		chatMode: ChatMode;
		chatSelections?: {
			prevSelns?: StagingSelectionItem[];
			currSelns?: StagingSelectionItem[];
		};
		agentParamsFromPlugin?: Record<string, any>;
		taskInfo?: TaskInfo;
	}) {
		const taskId = taskInfo?.taskId;
		const externalTools = taskInfo?.externalTools;
		const thread = this.getCurrentThread();
		const threadId = thread.id;

		const currSelns: StagingSelectionItem[] = chatSelections?.currSelns ?? thread.state.stateSelections.list;
		const state = {
			stagingSelections: [...currSelns],
			isBeingEdited: false,
		};

		// add user's message to chat history
		const content = await chat_userMessageContent(
			userMessageOpts,
			currSelns,
			this._codeseekFileService,
			this._codeSeekExporerService,
			this._modelService,
			this._workspaceContextService,
			this._codeseekSettingsService,
			this._urlContentFetcher,
			this._notificationService,
			this._zteUserInfoService,
		);

		const userHistoryElt: ChatMessage = { role: 'user', content, displayContent: userMessageOpts.userMessage, state };
		this._setFirstUserMessage(threadId, userMessageOpts.userMessage)
		this._addMessageToThread(threadId, userHistoryElt);

		this._setStreamState(threadId, { error: undefined });

		const codebaseSelections = await this.getCodeBaseSelections(userMessageOpts);

		const userMessageContent = await chat_userMessageContent(
			userMessageOpts,
			[...currSelns, ...codebaseSelections],
			this._codeseekFileService,
			this._codeSeekExporerService,
			this._modelService,
			this._workspaceContextService,
			this._codeseekSettingsService,
			this._urlContentFetcher,
			this._notificationService,
			this._zteUserInfoService,
		);

		const internalTools: InternalToolInfo[] | undefined = undefined;
		const agentParamsFromIde = await this._codeseekTaskService.provideIdeParams();
		// agent loop
		const agentLoop = async () => {

			let shouldSendAnotherMessage = true;
			let nMessagesSent = 0;
			const toolCallResult: ToolCallResultType | undefined = undefined;

			while (shouldSendAnotherMessage) {
				shouldSendAnotherMessage = false;
				nMessagesSent += 1;

				let res_: () => void;
				const awaitable = new Promise<void>((res, rej) => { res_ = res; });

				// replace last userMessage with userMessageFullContent (which contains all the files too)
				const messages_ = this.state.currentThreadMessages.map(m => (toLLMChatMessage(m)));
				const lastUserMsgIdx = findLastIndex(messages_, m => m.role === 'user');
				let messages = messages_;
				if (lastUserMsgIdx !== -1) { // should never be -1
					messages = [
						...messages.slice(0, lastUserMsgIdx),
						{ role: 'user', content: userMessageContent },
						...messages.slice(lastUserMsgIdx + 1, Infinity)];
				}
				const chatMessageEmitters_ = (): SendLLMType => {
					if (chatMode === ChatMode.Agent) {
						return {
							messagesType: 'agentMessages',
							messages: [
								{ role: 'user', content: userMessageContent }
							],
							tools: internalTools,
						};
					}
					else {
						const modelSelection = this._codeseekSettingsService.state.modelSelectionOfFeature[FeatureNames.CtrlL];
						let systemMessageContent = '';
						if (userMessageOpts.from === 'Fix') {
							systemMessageContent = fix_systemMessage(modelSelection?.modelName ?? '');
						} else {
							systemMessageContent = chat_systemMessage(modelSelection?.modelName ?? '');
						}
						return {
							messagesType: 'chatMessages',
							messages: [
								{ role: 'system', content: systemMessageContent },
								{ role: 'user', content: user_rules(this._codeseekSettingsService.state.globalSettings.userRules) },
								...messages,
							],
							tools: internalTools,
						};
					}
				};
				const chatMessageEmitters = chatMessageEmitters_();
				this._codeseekLogService.info('Prompt:', JSON.stringify(chatMessageEmitters));
				const startTime = Date.now();
				let firstTokenTime: number | null = null;
				let response = '';
				const reporter = (error?: any) => {
					const firstTokenCostTime = firstTokenTime ? firstTokenTime - startTime : 0;
					const totalCostTime = Date.now() - startTime;
					const request = {
						...chatMessageEmitters,
						useProviderFor: FeatureNames.CtrlL,
						agentParamsFromPlugin,
						agentParamsFromIde: agentParamsFromIde,
					};
					if (chatMode === ChatMode.Ask) {
						this._metricsService.capture(METRICS_EVENT.CHAT, { firstTokenCostTime, totalCostTime, request, response, error });
					} else if (chatMode === ChatMode.Agent) {
						this._metricsService.capture(METRICS_EVENT.AGENT, { firstTokenCostTime, totalCostTime, request, response, error });
					}
				}
				const llmCancelToken = this._llmMessageService.sendLLMMessage({
					...chatMessageEmitters,
					useProviderFor: FeatureNames.CtrlL,
					logging: { loggingName: chatMode },
					agentParamsFromPlugin,
					agentParamsFromIde: agentParamsFromIde,

					onText: ({ fullText }) => {
						firstTokenTime = firstTokenTime ?? Date.now();
						this._setStreamState(threadId, { messageSoFar: fullText });
						if (taskId) {
							this._codeseekTaskService.fireReceiveMessage(taskId, { message: { fullText: fullText } });
						}
					},
					onToolCall: async ({ fullText, toolCall }) => {
						this._addMessageToThread(threadId, { role: 'assistant', content: fullText, displayContent: fullText }, false);
						this._setStreamState(threadId, { messageSoFar: undefined });
						this._setStreamState(threadId, {
							toolCall: {
								...toolCall,
								content: '',
								result: null,
							}
						});
						const toolCallResult: ToolCallResultType = await this.executeTool(toolCall, threadId, taskId, externalTools);
						this._setStreamState(threadId, { toolCall: undefined });
						this._addMessageToThread(threadId, {
							role: 'tool', name: toolCall.name, params: toolCall.params, id: toolCall.id, content: toolCallResult.content, result: toolCallResult.result
						});
						this._llmMessageService.updateToolCallResult(llmCancelToken, toolCallResult);
						if (taskId) {
							this._codeseekTaskService.fireReceiveMessage(taskId, { message: { fullText: fullText, toolCall: toolCall, toolCallResult: toolCallResult } });
						}
					},
					onFinalMessage: async ({ fullText, toolCalls }) => {
						this._codeseekLogService.info(`Final message, threadId: ${threadId}, fullText: `,
							fullText, `toolCalls: `, JSON.stringify(toolCalls));
						response = fullText;
						reporter();
						if ((toolCalls?.length ?? 0) === 0) {
							this._finishStreamingTextMessage(threadId, fullText);
						}
						else {
							this._addMessageToThread(threadId, { role: 'assistant', content: fullText, displayContent: fullText });
							this._setStreamState(threadId, { messageSoFar: undefined }); // clear streaming message
							for (const tool of toolCalls ?? []) {
								const toolCallResult: ToolCallResultType = await this.executeTool(tool, threadId, taskId, externalTools);
								if (toolCallResult.code === ToolCallResultCode.failure) {
									shouldSendAnotherMessage = false;
									break;
								}
								this._addMessageToThread(threadId, { role: 'tool', name: tool.name, params: tool.params, id: tool.id, content: toolCallResult.content, result: toolCallResult.result });
								shouldSendAnotherMessage = true;

							}
						}
						if (taskId) {
							this._codeseekTaskService.fireReceiveMessage(taskId, { message: { fullText: fullText, toolCallResult: toolCallResult } });
							this._codeseekTaskService.fireTaskDone(taskId);
							this._codeseekSettingsService.setChatMode(ChatMode.Ask);
						}
						res_();
					},
					onError: (error) => {
						this._finishStreamingTextMessage(threadId, this.streamState[threadId]?.messageSoFar ?? '', error);
						if (taskId) {
							this._codeseekTaskService.fireTaskError(taskId);
							this._codeseekSettingsService.setChatMode(ChatMode.Ask);
						}
						res_();
						reporter(error);
					},
				});
				if (llmCancelToken === null) break;
				this._setStreamState(threadId, { streamingToken: llmCancelToken, isStreaming: true });

				await awaitable;
			}
		};

		agentLoop(); // DO NOT AWAIT THIS, this fn should resolve when ready to clear inputs

	}

	cancelStreaming(threadId: string) {
		const llmCancelToken = this.streamState[threadId]?.streamingToken;
		if (llmCancelToken !== undefined) this._llmMessageService.abort(llmCancelToken);
		this._finishStreamingTextMessage(threadId, this.streamState[threadId]?.messageSoFar ?? '');
	}

	dismissStreamError(threadId: string): void {
		this._setStreamState(threadId, { error: undefined });
	}

	async executeTool(toolCall: ToolCallType,
		threadId: string,
		taskId?: string,
		externalTools?: { toolName: string; toolDesc: string; needApprove: boolean; }[]): Promise<ToolCallResultType> {
		const toolName = toolCall.name as ToolName;
		let toolResultVal: ToolCallReturnType[ToolName];
		let content: string = '';
		const ideTool = this._toolsService.toolFns[toolName];

		const onWait = async () => {
			this._setStreamState(threadId, { isStreaming: false });
			const askMessage: AskMessage = {
				type: 'tool',
				content: toolCall,
			};
			this.getCurrentThread().state.askMessage = askMessage;
			await pWaitFor(() => this.state.allThreads[this.state.currentThreadId].state.askResponse !== undefined, { interval: 100 });
			const currentThreadState = this.state.allThreads[this.state.currentThreadId].state;
			const response = { type: currentThreadState.askResponse!.type, response: currentThreadState.askResponse?.response, text: currentThreadState.askResponse?.text };
			currentThreadState.askResponse = undefined;
			currentThreadState.askResponseText = undefined;
			return response;
		};
		if (ideTool === undefined) {
			let externalToolsNeedApprove = true;
			const filteredTools = externalTools?.filter(tool => tool.toolName === toolName);
			if (filteredTools && filteredTools.length > 0) {
				externalToolsNeedApprove = filteredTools[0].needApprove;
			}
			if (externalToolsNeedApprove) {
				toolResultVal = await this._toolsService.toolFns[ToolNameEnum.APPROVE_REQUEST](toolCall.params as any, onWait);
				const approveResult = toolResultVal as ApproveRequestResultType;
				if (approveResult.response === AskReponseType.yesButtonClicked && taskId) {
					this._codeseekTaskService.fireToolCall(taskId, toolName, toolCall.params);
				}
				return { code: ToolCallResultCode.success, name: toolName, result: toolResultVal, error: '', content };
			}
			this._setStreamState(threadId, { isStreaming: true });
			if (taskId) {
				this._codeseekTaskService.fireToolCall(taskId, toolName, toolCall.params);
				return { code: ToolCallResultCode.success, name: toolName, result: true, error: '', content };
			}
		}

		try {
			if (this._toolsService.isNeedApprove(toolName)) {
				toolResultVal = await this._toolsService.toolFns[ToolNameEnum.APPROVE_REQUEST](toolCall.params as any, onWait);
				content = this._toolsService.toolResultToString[toolName](toolResultVal as any);
				const approveResult = toolResultVal as ApproveRequestResultType;
				if (approveResult.response === AskReponseType.noButtonClicked) {
					return { code: ToolCallResultCode.success, name: toolName, result: toolResultVal, error: '', content };
				}
			}
			this._setStreamState(threadId, { isStreaming: true });
			toolResultVal = await ideTool(toolCall.params as any);
			content = this._toolsService.toolResultToString[toolName](toolResultVal as any);
			return { code: ToolCallResultCode.success, name: toolName, result: toolResultVal, error: '', content };
		} catch (error) {
			return { code: ToolCallResultCode.failure, name: toolName, result: null, error: error.message, content };
		}
	}


	// ---------- the rest ----------

	isCurrentThreadWorking(): boolean {
		const currentThread = this.getCurrentThread();
		const streamState = this.streamState[currentThread.id];
		return streamState && streamState.streamingToken && !streamState.error ? true : false;
	}

	getCurrentThread(): ChatThreads[string] {
		return this.state.allThreads[this.state.currentThreadId];
	}
	getCurrentThreadMessages(): ChatMessage[] {
		return this.state.currentThreadMessages
	}
	getFocusedMessageIdx() {
		const thread = this.getCurrentThread();

		// get the focusedMessageIdx
		const focusedMessageIdx = thread.state.focusedMessageIdx;
		if (focusedMessageIdx === undefined) return;

		// check that the message is actually being edited
		const focusedMessage = this.state.currentThreadMessages[focusedMessageIdx];
		if (focusedMessage.role !== 'user') return;
		if (!focusedMessage.state) return;

		return focusedMessageIdx;
	}

	isFocusingMessage() {
		return this.getFocusedMessageIdx() !== undefined;
	}

	switchToThread(threadId: string) {
		const messages = this._loadChatMessages(threadId)
		this._setState({ currentThreadId: threadId, currentThreadMessages: messages }, true);
	}

	deleteThread(threadId: string) {
		const currentThread = this.getCurrentThread();
		const isCurrentThread = currentThread?.id === threadId;

		// 如果是当前线程，先取消流式传输
		if (isCurrentThread) {
			this.cancelStreaming(threadId);
		}

		// 删除allThreads中的线程
		const { allThreads } = this.state;
		delete allThreads[threadId];
		this._deleteThreadMessages(threadId)

		// 清理streamState中的相关状态
		if (this.streamState[threadId]) {
			delete this.streamState[threadId];
		}

		// 保存更新后的线程集合
		this._storeChatSummary();

		// 如果删除的是当前线程，需要切换到其他线程或创建新线程
		if (isCurrentThread) {
			const remainingThreadIds = Object.keys(allThreads);
			if (remainingThreadIds.length > 0) {
				// 切换到第一个可用的线程
				this.switchToThread(remainingThreadIds[0]);
			} else {
				// 如果没有可用的线程，创建一个新线程
				this.openNewThread();
			}
		} else {
			this._setState({ allThreads }, true);
		}
	}

	openNewThread() {
		const currentThread = this.getCurrentThread();
		if (currentThread && this.isCurrentThreadWorking()) {
			this.cancelStreaming(currentThread.id);
		}

		this._sidebarStateService.fireOpenNewChat();
		this._codeseekSettingsService.setChatMode(ChatMode.Ask);
		// if a thread with 0 messages already exists, switch to it
		const { allThreads } = this.state;
		for (const threadId in allThreads) {
			if (allThreads[threadId].messagesLength === 0) {
				allThreads[threadId].state.focusedMessageIdx = undefined;
				allThreads[threadId].state.stateSelections = { list: [], followEditorActive: true };
				allThreads[threadId].state.isCheckedOfSelectionId = {};
				this.switchToThread(threadId);
				this._addSelectionToChat();
				return;
			}
		}
		// otherwise, start a new thread
		const newThread = newThreadObject();

		// update state
		const newThreads: ChatThreads = {
			...allThreads,
			[newThread.id]: newThread
		};
		this._setState({ allThreads: newThreads, currentThreadId: newThread.id, currentThreadMessages: [] }, true);
		this._storeChatSummary();
		this._addSelectionToChat();
	}

	private _addSelectionToChat() {
		const selection = this._codeSeekCodeSelectionService.getFileSelction();
		this.addSelectionToChat(selection);
	}

	addSelectionToChat(selection?: StagingSelectionItem) {
		if (!selection) return;

		const focusedMessageIdx = this.getFocusedMessageIdx();
		let selections: StagingSelectionItem[] = [];
		let setSelections = (s: StagingSelectionItem[]) => { };

		if (focusedMessageIdx === undefined) {
			selections = this.getCurrentThreadStagingSelections();
			setSelections = (s: StagingSelectionItem[]) => this.setCurrentThreadStagingSelections(s);
		} else {
			selections = this.getCurrentMessageState(focusedMessageIdx).stagingSelections;
			setSelections = (s) => this.setCurrentMessageState(focusedMessageIdx, { stagingSelections: s });
		}

		const updatedSelections = selections.map(item => {
			if (item.type === 'File' && selection.type === 'File' &&
				item.fileURI.toString() === selection.fileURI.toString() &&
				item.fromActive) {
				return {
					...item,
					fromActive: false,
					fromEditor: true
				};
			}
			return item;
		});

		setSelections(this._mentionsService.addItemToSelectedFile(updatedSelections, selection));
	}

	_addMessageToThread(threadId: string, message: ChatMessage, affectCurrentThread: boolean = true) {
		const { allThreads } = this.state;

		const oldThread = allThreads[threadId];
		const newMessages = [...this.state.currentThreadMessages, message]

		// update state and store it
		const newThreads = {
			...allThreads,
			[oldThread.id]: {
				...oldThread,
				lastModified: new Date().toISOString(),
				messagesLength: newMessages.length,
				// messages: [...oldThread.messages, message],
			}
		};
		this._setState({ allThreads: newThreads, currentThreadMessages: newMessages }, affectCurrentThread); // the current thread just changed (it had a message added to it)
		this._storeChatSummary()
		this._storeChatMessages(oldThread.id, newMessages);
	}
	private _setFirstUserMessage(threadId: string, userMessage: string) {
		const allThreads = this.state.allThreads
		const thread = this.state.allThreads[threadId]
		if (thread.firstUserMessage === '') {
			const newThreads = {
				...allThreads,
				[thread.id]: {
					...thread,
					firstUserMessage: userMessage
				}
			}
			this._setState({ allThreads: newThreads }, false)
		}
	}
	// sets the currently selected message (must be undefined if no message is selected)
	setFocusedMessageIdx(messageIdx: number | undefined) {

		const threadId = this.state.currentThreadId;
		const thread = this.state.allThreads[threadId];
		if (!thread) return;

		this._setState({
			allThreads: {
				...this.state.allThreads,
				[threadId]: {
					...thread,
					state: {
						...thread.state,
						focusedMessageIdx: messageIdx,
					}
				}
			}
		}, true);
	}

	// set message.state
	private _setCurrentMessageState(state: Partial<UserMessageState>, messageIdx: number): void {

		const threadId = this.state.currentThreadId;
		const thread = this.state.allThreads[threadId];
		if (!thread) return;

		this._setState({
			currentThreadMessages: this.state.currentThreadMessages.map((m, i) =>
				i === messageIdx && m.role === 'user' ? {
					...m,
					state: {
						...m.state,
						...state
					},
				} : m
			)
		}, true);

	}

	// set thread.state
	private _setCurrentThreadState(state: Partial<ThreadType['state']>): void {

		const threadId = this.state.currentThreadId;
		const thread = this.state.allThreads[threadId];
		if (!thread) return;

		this._setState({
			allThreads: {
				...this.state.allThreads,
				[thread.id]: {
					...thread,
					state: {
						...thread.state,
						...state
					}
				}
			}
		}, true);

	}

	getCurrentThreadStagingSelections = () => {
		return this.getCurrentThread().state.stateSelections.list;
	};

	getCurrentThreadStateSelections = () => {
		return this.getCurrentThread().state.stateSelections;
	};

	setCurrentThreadStagingSelections = (stagingSelections: StagingSelectionItem[]) => {
		const stateSelections = this.getCurrentThread().state.stateSelections;
		stateSelections.list = [...stagingSelections];
		this._setCurrentThreadState({ stateSelections });
	};

	// gets `staging` and `setStaging` of the currently focused element, given the index of the currently selected message (or undefined if no message is selected)

	getCurrentMessageState(messageIdx: number): UserMessageState {
		const currMessage = this.state.currentThreadMessages[messageIdx];
		if (!currMessage || currMessage.role !== 'user') return defaultMessageState;
		return currMessage.state;
	}

	setCurrentMessageState(messageIdx: number, newState: Partial<UserMessageState>) {
		const currMessage = this.state.currentThreadMessages[messageIdx];
		if (!currMessage || currMessage.role !== 'user') return;
		this._setCurrentMessageState(newState, messageIdx);
	}

	setCurrentThreadStateSelectionsChangeSelections() {
		const stagingSelections = this.getCurrentThreadStateSelections();
		stagingSelections.followEditorActive = false;
	}

	private onDidActiveEditorChange() {
		const selections = this.getCurrentThreadStagingSelections();
		if (selections.length === 0) {
			this._addSelectionToChat();
		} else {
			if (this.state.currentThreadMessages.length === 0) {
				const selection = this._codeSeekCodeSelectionService.getFileSelction();
				if (!selection) {
					return;
				}

				let hasSameUriFromEditor = false;
				const filteredSelections = selections.filter(s => {
					if (s.type === 'File') {
						if (s.fileURI.toString() === selection.fileURI.toString() && s.fromEditor) {
							hasSameUriFromEditor = true;
							return true;
						}
						return !s.fromActive;
					}
					return true;
				});
				if (!hasSameUriFromEditor) {
					this.setCurrentThreadStagingSelections([selection, ...filteredSelections]);
				} else {
					this.setCurrentThreadStagingSelections(filteredSelections);
				}
			}
		}
	}
}

registerSingleton(IChatThreadService, ChatThreadService, InstantiationType.Eager);
