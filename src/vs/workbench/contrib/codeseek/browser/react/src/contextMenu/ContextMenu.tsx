import React, { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from "react"
import { ContextMenuOptionType, ContextMenuQueryItem, getContextMenuOptions } from "./context-mentions.js"
import { removeLeadingNonAlphanumeric } from "../common/CodeAccordian.js"
import { VSCodeFileIcon } from '../sidebar-tsx/SidebarChat.js'
import { FileKind, IFileStat } from '../../../../../../../platform/files/common/files.js'
import { useAccessor } from '../util/services.js'
import { StorageScope, StorageTarget } from '../../../../../../../platform/storage/common/storage.js'
import { CancellationToken, CancellationTokenSource } from '../../../../../../../base/common/cancellation.js'
import { basename, dirname, isEqual, relativePath, resolvePath } from '../../../../../../../base/common/resources.js'
import { URI } from '../../../../../../../base/common/uri.js'

interface FileTreeItem {
	type: ContextMenuOptionType;
	value?: string;
	label?: string;
	description?: string;
	icon?: string;
	uri?: URI;
	children?: FileTreeItem[];
	isExpanded?: boolean;
	level: number;
}

interface ContextMenuProps {
	onSelect: (option: ContextMenuQueryItem) => void
	searchQuery: string
	onMouseDown: () => void
	mouseSelectedIndex: number
	setMouseSelectedIndex: (index: number) => void
	arrowSelectedIndex: number
	setArrowSelectedIndex: (index: number) => void
	selectedType: ContextMenuOptionType | null
	queryItems: ContextMenuQueryItem[]
	textAreaRect: DOMRect | undefined;
	setSelectedType: (type: ContextMenuOptionType | null) => void
	focusOnTextArea: () => void
}

const TREE_VIEW_STATE_KEY = 'workbench.codeseek.explorer.treeViewState';
const ContextMenu: React.FC<ContextMenuProps> = ({
	onSelect,
	searchQuery,
	onMouseDown,
	mouseSelectedIndex,
	setMouseSelectedIndex,
	arrowSelectedIndex,
	setArrowSelectedIndex,
	selectedType,
	queryItems,
	textAreaRect,
	setSelectedType,
	focusOnTextArea,
}): JSX.Element => {
	const menuRef = useRef<HTMLDivElement>(null)
	const [treeItems, setTreeItems] = useState<FileTreeItem[]>([])
	const [isLoadingTree, setIsLoadingTree] = useState(false)
	const [showFileTree, setShowFileTree] = useState(false)

	const [positionStyle, setPositionStyle] = useState<{ top?: string; bottom?: string, display: string }>({ display:'none'})
	const [treeSearchQuery, setTreeSearchQuery] = useState("")
	const [hasMoreOptions, setHasMoreOptions] = useState(false)

	const accessor = useAccessor();

	// 获取工作空间文件树
	const loadWorkspaceTree = useCallback(async () => {
		setIsLoadingTree(true)
		try {
			const fileService = accessor.get('IFileService')
			const workspaceService = accessor.get('IWorkspaceContextService')
			const storageService = accessor.get('IStorageService')

			const treeViewState = storageService.get(TREE_VIEW_STATE_KEY, StorageScope.WORKSPACE)
			const expandedPaths = treeViewState ? new Set(JSON.parse(treeViewState).expanded) : new Set()

			const workspaceRoot = workspaceService.getWorkspace().folders[0].uri
			const rootStat = await fileService.resolve(workspaceRoot, {
				resolveMetadata: true
			})

			const convertToTreeItems = async (stat: IFileStat, level: number, parentPath?: string): Promise<FileTreeItem> => {
				const path = parentPath ? `${parentPath}/${stat.name}` : stat.name
				const isExpanded = expandedPaths.has(stat.resource.path);
				let children: IFileStat[] | undefined = stat.children
				if(isExpanded && !children) {
					const statWithMetadata = await fileService.resolve(stat.resource, {
						resolveMetadata: true
					})
					children = statWithMetadata.children
				}
				return {
					type: stat.isDirectory ? ContextMenuOptionType.Folder : ContextMenuOptionType.File,
					label: stat.name,
					value: path,
					uri: stat.resource,
					level,
					isExpanded,
					children: await Promise.all(children?.map((child) =>
						convertToTreeItems(child, level + 1, path)
					) || [])
				}
			}

			// 直接使用根目录的子项作为顶层项目
			const items = await Promise.all(rootStat.children?.map(child =>
				convertToTreeItems(child, 0)
			) || [])
			setTreeItems(items)
		} catch (error) {
		}
		setIsLoadingTree(false)
	}, [setTreeItems, setIsLoadingTree])

	// 处理文件夹展开/折叠
	const toggleFolder = useCallback(async (uri: URI | undefined) => {
		if(!uri) {
			return
		}
		const storageService = accessor.get('IStorageService');
		const fileService = accessor.get('IFileService');
		const treeViewState = storageService.get(TREE_VIEW_STATE_KEY, StorageScope.WORKSPACE);
		let expandedPaths = treeViewState ? new Set(JSON.parse(treeViewState).expanded) : new Set();

		const workspaceService = accessor.get('IWorkspaceContextService');
		const workspaceRoot = workspaceService.getWorkspace().folders[0].uri;


		const updateTreeItems = async (items: FileTreeItem[]): Promise<FileTreeItem[]> => {
		  const updatedItems = await Promise.all(items.map(async (item) => {
			if (isEqual(item.uri, uri)) {
			  const newIsExpanded = !item.isExpanded;

			  // 如果是展开操作且没有子项或子项为空数组，尝试加载子项
			  if (newIsExpanded && item.type === ContextMenuOptionType.Folder && (
			  !item.children || item.children.length === 0) && item.uri) {
				try {
				  const stat = await fileService.resolve(item.uri, {
					resolveMetadata: true
				  });
				  // 并发创建子节点
				  const childPromises = stat.children?.map(async (child: IFileStat) => {
					const childItem: FileTreeItem = {
					  type: child.isDirectory ? ContextMenuOptionType.Folder : ContextMenuOptionType.File,
					  label: child.name,
					  value: relativePath(workspaceRoot, child.resource),
					  uri: child.resource,
					  level: item.level + 1,
					  isExpanded: false,
					  children: undefined
					};

					return childItem;
				  }) || [];

				  item.children = await Promise.all(childPromises);

				  return { ...item, isExpanded: newIsExpanded, children: item.children };
				} catch (error) {
				  return { ...item, isExpanded: newIsExpanded };
				}
			  }

			  // 如果是折叠操作，直接更新状态
			  if (!newIsExpanded) {
				expandedPaths.delete(item.uri?.path);
			  } else {
				expandedPaths.add(item.uri?.path);
			  }
			  return { ...item, isExpanded: newIsExpanded };
			}

			if (item.children) {
			  return { ...item, children: await updateTreeItems(item.children) };
			}

			return item;
		  }));

		  return updatedItems;
		};

		const newItems = await updateTreeItems(treeItems);
		// 保存展开状态
		storageService.store(
		  TREE_VIEW_STATE_KEY,
		  JSON.stringify({ expanded: Array.from(expandedPaths) }),
		  StorageScope.WORKSPACE,
		  StorageTarget.USER
		);
		setTreeItems(newItems);
	  }, [accessor, treeItems]);

	// 获取扁平化的树项目列表（用于渲染）
	const getFlattenedItems = useCallback((items: FileTreeItem[]): FileTreeItem[] => {
		const flattened: FileTreeItem[] = []

		const flatten = (items: FileTreeItem[]) => {
			items.forEach(item => {
				flattened.push(item)
				if (item.isExpanded && item.children) {
					flatten(item.children)
				}
			})
		}

		flatten(items)
		return flattened
	},[])

	useEffect(() => {
		if (menuRef.current) {
			const selectedElement = menuRef.current.children[arrowSelectedIndex] as HTMLElement
			if (selectedElement) {
				const menuRect = menuRef.current.getBoundingClientRect()
				const selectedRect = selectedElement.getBoundingClientRect()

				if (selectedRect.bottom > menuRect.bottom) {
					menuRef.current.scrollTop += selectedRect.bottom - menuRect.bottom
				} else if (selectedRect.top < menuRect.top) {
					menuRef.current.scrollTop -= menuRect.top - selectedRect.top
				}
			}
		}
	}, [arrowSelectedIndex])

	const renderOptionContent = ({
		option,
		className
	}: {
		option: ContextMenuQueryItem,
		className: string
	}) => {
		switch (option.type) {
			case ContextMenuOptionType.Problems:
				return <span className={className}>Problems</span>
			case ContextMenuOptionType.URL:
				return <span className={className}>Paste URL to fetch contents</span>
			case ContextMenuOptionType.NoResults:
				return <span className={className}>No results found</span>
			case ContextMenuOptionType.Git:
				if (option.value) {
					return (
						<div style={{ display: "flex", flexDirection: "column", gap: 0 }}>
							<span className={`${className} line-height-1.2`}>{option.label}</span>
							<span className='text-[0.85em] opacity-70 whitespace-nowrap overflow-hidden text-ellipsis line-height-1.2'>
								{option.description}
							</span>
						</div>
					)
				} else {
					return <span className={className}>Git Commits</span>
				}
			case ContextMenuOptionType.File:
			case ContextMenuOptionType.Folder:
				return (<>
					<span className={`${className} text-[0.9em] whitespace-nowrap`}>
						{option.value!.split('/').pop()}
					</span>
					<span className='text-[0.73em] whitespace-nowrap overflow-hidden text-ellipsis text-left ml-[10px] opacity-60'
						style={{
							direction: "rtl",
						}}
					>
						{removeLeadingNonAlphanumeric(option.value || "") + "\u200E"}
					</span>
				</>);
			case ContextMenuOptionType.ExplorerView:
				return <span className={className}>More...</span>

			case ContextMenuOptionType.Codebase:
				return <span className={className}>Codebase</span>
		}
	}

	const getIconForOption = (option: ContextMenuQueryItem): string | null => {
		switch (option.type) {
			case ContextMenuOptionType.File:
				return "file"
			case ContextMenuOptionType.Folder:
				return "folder"
			case ContextMenuOptionType.Problems:
				return "warning"
			case ContextMenuOptionType.URL:
				return "link"
			case ContextMenuOptionType.Git:
				return "git-commit"
			case ContextMenuOptionType.NoResults:
				return "info"
			case ContextMenuOptionType.Codebase:
				return "layers"
			default:
				return null
		}
	}

	const isOptionSelectable = (option: ContextMenuQueryItem): boolean => {
		return option.type !== ContextMenuOptionType.NoResults && option.type !== ContextMenuOptionType.URL
	}

	useEffect(() => {
		const setDropdownPosition = (direction: 'bottom' | 'top', textAreaHeight: number)  => {
			if (direction === 'bottom') {
				setPositionStyle({ top: textAreaHeight + 20 + "px", display: 'block' })
			} else {
				setPositionStyle({ bottom: textAreaHeight + 40 + "px", display: 'block' })
			}
		}

		if(textAreaRect) {
			setDropdownPosition(textAreaRect.y > 300 ? 'top' : "bottom", textAreaRect.height)
		}
	}, [textAreaRect])

	useEffect(() => {
		setShowFileTree(selectedType === ContextMenuOptionType.ExplorerView)
	}, [selectedType])

	const filterSearchResult = async (searchResults: URI[], treeSearchQuery: string, rootPath: string) => {
		const travel = (uri: URI) => {
			if(uri.path.toLowerCase().includes(locaseTreeSearchQuery) && uri.path.startsWith(rootPath)) {
				const name = basename(uri)
				if(name.toLowerCase().includes(locaseTreeSearchQuery)) {
					return uri;
				}
				return travel(dirname(uri))
			}
			return null;
		}
		const result: URI[] = []
		const locaseTreeSearchQuery = treeSearchQuery.toLowerCase()

		searchResults = searchResults.filter(item => item.path.toLowerCase().includes(locaseTreeSearchQuery))
		if(searchResults.length > 30) {
			searchResults =  searchResults.slice(0,30)
			setHasMoreOptions(true)
		}
		const travelPromises = searchResults.map(item => travel(item));
		const targetUris = await Promise.all(travelPromises);
		for (const targetUri of targetUris) {
			if (targetUri && !result.some(uri => isEqual(uri, targetUri))) {
				result.push(targetUri);
			}
		}
		return result
	}
	// 添加一个递归搜索所有节点的函数
	const searchAllTreeItems = async (treeSearchQuery: string, token: CancellationToken): Promise<FileTreeItem[]> => {
		const fileService = accessor.get('IFileService')
		const codebaseSearchService = accessor.get('ICodebaseSearchService')
		const workspaceService = accessor.get('IWorkspaceContextService')
		const results: FileTreeItem[] = []
		const processedPaths = new Set<string>()

		try {
			// 获取工作空间根路径
			const workspaceRoot = workspaceService.getWorkspace().folders[0].uri
			const rootPath = workspaceRoot.path
			const searchResults = await codebaseSearchService.fileSearch(treeSearchQuery)

			const targetResult = await filterSearchResult(searchResults, treeSearchQuery, rootPath)

			for (const result of targetResult) {
				// 将完整路径转换为相对于工作空间的路径
				const relativePathStr = relativePath(workspaceRoot, result)
				if(!relativePathStr) {
					continue
				}
				const pathParts = relativePathStr.split('/')

				// 处理所有父目录路径
				for (let i = 0; i < pathParts.length - 1; i++) {
					const parentPath = pathParts.slice(0, i + 1).join('/')

					if (!processedPaths.has(parentPath)) {
						processedPaths.add(parentPath)

						try {
							const parentUri = resolvePath(workspaceRoot, parentPath)

							const parentItem: FileTreeItem = {
								type: ContextMenuOptionType.Folder,
								label: pathParts[i],
								value: parentPath, // 使用相对路径
								uri: parentUri,
								level: i,
								isExpanded: true,
								children: [],
							}
							results.push(parentItem)
						} catch (error) {
							console.error('Failed to resolve parent directory:', error)
						}
					}
				}
				// 处理搜索到的文件/文件夹本身
				try {
					const stat = await fileService.resolve(result, {
						resolveMetadata: true
					})

					const item: FileTreeItem = {
						type: stat.isDirectory ? ContextMenuOptionType.Folder : ContextMenuOptionType.File,
						label: pathParts[pathParts.length - 1],
						value: relativePathStr, // 使用相对路径
						uri: result,
						level: pathParts.length - 1,
						isExpanded: false,
						children: [],
					}

				//当前文件夹已经添加过
				if (!processedPaths.has(relativePathStr)) {
					processedPaths.add(relativePathStr)
					results.push(item)
				}
				} catch (error) {
					console.error('Failed to resolve file:', error)
				}
			}

			// 按照路径排序
			results.sort((a, b) => a.value?.localeCompare(b.value || '') || 0)
			return results
		} catch (error) {
			console.error('Failed to search files:', error)
			return []
		}
	}

	// 添加状态来存储搜索结果
	const [filteredItems, setFilteredItems] = useState<FileTreeItem[]>([])

	// 添加搜索loading状态
	const [isSearching, setIsSearching] = useState(false)
	const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(treeSearchQuery);
	useEffect(() => {
		const handler = setTimeout(() => {
			setDebouncedSearchQuery(treeSearchQuery);
		}, 200); // 200毫秒的防抖延迟

		return () => {
			clearTimeout(handler);
		};
	}, [treeSearchQuery]);

	const treeOptions = useMemo(() => {
		if (!debouncedSearchQuery) {
			return getFlattenedItems(treeItems);
		} else {
			return filteredItems;
		}
	}, [debouncedSearchQuery, treeItems, filteredItems]);
	// 修改过滤选项的逻辑
	const filteredOptions = useMemo(() => {
		return getContextMenuOptions(searchQuery, queryItems)
	}, [searchQuery, queryItems])

	const hanldeTreeSelect = useCallback((item: FileTreeItem) => {
		onSelect({...item})
	},[onSelect])

	const abortControllerRef = useRef<CancellationTokenSource | null>(null);

	useEffect(() => {
		setHasMoreOptions(false)
		if (showFileTree && treeSearchQuery) {
			const search = async () => {
				// 取消之前的搜索
				if (abortControllerRef.current) {
					abortControllerRef.current.cancel();
					abortControllerRef.current.dispose();
				}

				const tokenSource = new CancellationTokenSource();
				abortControllerRef.current = tokenSource;

				setIsSearching(true);
				try {
					const results = await searchAllTreeItems(treeSearchQuery, tokenSource.token);
					if (!tokenSource.token.isCancellationRequested) {
						setFilteredItems(results);
					}
				} catch (error) {
					// if (error instanceof Error && error.name !== 'AbortError') {
					// 	console.error('Search failed:', error);
					// }
				} finally {
					if (!tokenSource.token.isCancellationRequested) {
						setIsSearching(false);
					}
					tokenSource.dispose();
				}
			};
			search();

			return () => {
				if (abortControllerRef.current) {
					abortControllerRef.current.cancel();
					abortControllerRef.current.dispose();
					abortControllerRef.current = null;
				}
			};
		}
	}, [treeSearchQuery, showFileTree]);

	useEffect(() => {
		if (showFileTree) {
			// 初始加载树结构
			loadWorkspaceTree();

			// 订阅文件系统变化事件
			const fileService = accessor.get('IFileService');
			const disposable = fileService.onDidFilesChange((e: { gotAdded: () => boolean; gotDeleted: () => boolean }) => {
				// 当文件被增删改时，重新加载树结构
				if (e.gotAdded() || e.gotDeleted()) {
					loadWorkspaceTree();
				}
			});

			// 清理函数
			return () => {
				disposable.dispose();
			};
		}
	}, [showFileTree]);

	useEffect(()=> {
		setMouseSelectedIndex(0)
		setArrowSelectedIndex(0)
	}, [treeSearchQuery])

	const handleKeyDown = useCallback((event: React.KeyboardEvent<HTMLInputElement>, preIndex: number) => {

		if (event.key === "Escape") {
			setSelectedType(null);
			setShowFileTree(false);
			focusOnTextArea()
			return;
		}

		if (event.key === "ArrowUp" || event.key === "ArrowDown") {
			event.preventDefault()
				const direction = event.key === "ArrowUp" ? -1 : 1
				const optionsLength = treeOptions.length

				if (optionsLength === 0) {
					return
				}

				const newSelectableIndex =
					(preIndex + direction + treeOptions.length) %
					treeOptions.length
				setArrowSelectedIndex(newSelectableIndex)
			return
		}
		if ((event.key === "Enter" || event.key === "Tab") && preIndex !== -1) {
			event.preventDefault();
			const selectedItem = treeOptions[preIndex]
			if(!selectedItem) {
				return
			}
			if (selectedItem.type === ContextMenuOptionType.File)
			{
				onSelect(selectedItem)
			} else if (selectedItem.type === ContextMenuOptionType.Folder) {
				toggleFolder(selectedItem.uri)
			}
			return;
		}
	},
	[
		setArrowSelectedIndex,
		onSelect,
		treeOptions
	]);

	return (
		<div
			className='border border-codeseek-border-1 rounded-md'
			style={{
				position: "absolute",
				...positionStyle,
				left: 15,
				right: 15,
				overflowX: "hidden",
				zIndex: 100,
			}}
			onMouseDown={onMouseDown}>
			{showFileTree && (
				// 搜索输入框移到滚动区域外
				<div className="p-1 bg-codeseek-bg-2">
					<div
						className="flex items-center bg-codeseek-bg-3 rounded-sm px-2 border border-codeseek-border-3"
						onClick={(e) => e.stopPropagation()}
					>
						<input
							type="text"
							value={treeSearchQuery}
							onChange={(e) => setTreeSearchQuery(e.target.value)}
							onClick={(e) => e.stopPropagation()}
							onMouseDown={(e) => e.stopPropagation()}
							className="w-full bg-transparent outline-none text-sm py-1 text-codeseek-fg-1"
							placeholder="Search files..."
							autoFocus
							onKeyDown={(e) =>handleKeyDown(e, arrowSelectedIndex)}
						/>
						{isSearching ? (
							<i className="codicon codicon-loading spin text-codeseek-fg-3 mr-1 scale-75" />
						) : (
							<i className="codicon codicon-search text-codeseek-fg-3 mr-1 scale-75" />
						)}
					</div>
				</div>
			)}
			<div className={`${showFileTree ? 'border-t border-codeseek-border-1' : ''}`}>
				<div
					ref={menuRef}
					className={`
						bg-codeseek-bg-2
						shadow-[0_4px_10px_rgba(0,0,0,0.25)]
						z-[1000]
						flex flex-col
						${showFileTree ? 'max-h-[600px]' : 'max-h-[200px]'}
						overflow-y-auto
						[&::-webkit-scrollbar]:w-[1px]
						[&::-webkit-scrollbar-thumb]:bg-gray-500
						[&::-webkit-scrollbar-thumb]:rounded-[1px]
					`}>
					{isLoadingTree ? (
						<div className="p-2 text-center">
							<i className="codicon codicon-loading spin" /> Loading...
						</div>
					) : (
						<>
							{showFileTree ? (
								// 显示树形结构
								<>
								{treeOptions.map((option, index) => (
									<div
										key={`${option.type}-${option.value}-${option.uri!.path}-${index}`}
										onClick={() => {
											if(isOptionSelectable(option)) {
												if(option.type == ContextMenuOptionType.Folder) {
													toggleFolder(option.uri)
												} else {
													hanldeTreeSelect(option)
												}
											}}
										}
										className={`px-[6px] py-[2px] flex items-center
											cursor: ${isOptionSelectable(option) ? "pointer" : "default"}
											${index === mouseSelectedIndex ? "bg-vscode-select-bg" : "hover:bg-vscode-select-hover-bg"}
											${index === arrowSelectedIndex ? "border border-codeseek-border-focus" : ""}
										`}
										onMouseEnter={() => isOptionSelectable(option) && setMouseSelectedIndex(index)}
									>
										<div style={{
											display: "flex",
											alignItems: "center",
											paddingLeft: `${option.level * 12}px`,
											flex: 1,
										}}>
											{option.type === ContextMenuOptionType.Folder && (
												<i
													className={`mr-[4px] codicon codicon-chevron-${option.isExpanded ? 'down' : 'right'} ${index === mouseSelectedIndex ? "text-white" : ""}`}
													style={
														{
															fontSize: "0.9em",
														}
													}
													onClick={(e) => {
														e.stopPropagation()
														toggleFolder(option.uri)
													}}
												/>
											)}
											{/* <i className={`codicon codicon-${option.type === ContextMenuOptionType.Folder ? 'folder' : 'file'}`}
												style={{ marginRight: '6px' }} /> */}
											{(option.type === ContextMenuOptionType.File && option.uri && (
													<div className="w-5 h-5 flex items-center justify-center flex-shrink-0">
														<VSCodeFileIcon uri={option.uri!} fileKind={FileKind.FILE} />
													</div>
												)
											)}
											<span className={`pt-[1px] pb-[2px] text-md ${index === mouseSelectedIndex ? "text-white" : ""}`}>{option.value?.split('/').pop()}</span>
										</div>
									</div>
								))}
								</>
							) : (
								// 显示普通选项
								filteredOptions.map((option, index) => (
									<div
										key={`${option.type}-${option.value || index}`}
										onClick={() => isOptionSelectable(option) && onSelect(option)}
										className={`flex items-center justify-between cursor-pointer px-[6px]
											${index === mouseSelectedIndex && isOptionSelectable(option) ? 'bg-codeseek-bg-selected' : ''}
											${index === arrowSelectedIndex ? "border border-codeseek-border-focus" : ""}`
										}
										onMouseEnter={() => isOptionSelectable(option) && setMouseSelectedIndex(index)}>
										<div
											className={`flex items-center flex-1 min-w-0 overflow-hidden py-[2px]
											${index + 1 < filteredOptions.length && filteredOptions[index].type !== filteredOptions[index + 1].type ? 'border-b border-codeseek-border-3' : ''}`}
										>
											{(option.type === ContextMenuOptionType.File && option.uri ? (
													<div className="w-5 h-5 flex items-center justify-center flex-shrink-0">
														<VSCodeFileIcon uri={option.uri!} fileKind={FileKind.FILE} />
													</div>
												):(
													<div className="w-5 h-5 flex items-center justify-center flex-shrink-0">
														<span
														 	className={`codicon scale-75
																${getIconForOption(option) ? 'codicon-' + getIconForOption(option) : ''}
														`} />
													</div>
												)
											)}
											{renderOptionContent({
												option: option,
												className: `px-1 rounded-md text-md ${index === mouseSelectedIndex && isOptionSelectable(option) ? 'text-white' : ''}`
											})}
										</div>
										{(option.type === ContextMenuOptionType.File ||
											option.type === ContextMenuOptionType.Folder ||
											option.type === ContextMenuOptionType.Git) &&
											!option.value && (
												<i
													className="codicon codicon-chevron-right"
													style={{ fontSize: "0.9em", flexShrink: 0, marginLeft: 8 }}
												/>
											)}
									</div>
								))
							)}
						</>
					)}
				</div>
				{showFileTree && (
					treeOptions.length === 0 ? (
						<div className="pt-2 text-center text-codeseek-fg-3 bg-codeseek-bg-2">
							No matching results
						</div>
					):(hasMoreOptions && (
						<div className="pt-2 text-center text-codeseek-fg-3 bg-codeseek-bg-2">
							Is there nothing you want? Try precise search
						</div>
					))
				)}
			</div>
			{showFileTree && (
				<div
					className="flex items-center px-2 py-1 border-t border-codeseek-border-3 cursor-pointer bg-codeseek-bg-2 text-md"
					onClick={() => setSelectedType(null)}
				>
					<i className="codicon codicon-arrow-left mr-2 scale-75" />
					<span>Back to menu</span>
				</div>
			)}
		</div>
	)
}

export default ContextMenu
