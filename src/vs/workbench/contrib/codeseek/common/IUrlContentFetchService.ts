import { Disposable } from '../../../../base/common/lifecycle.js';
import { IChannel } from '../../../../base/parts/ipc/common/ipc.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { IMainProcessService } from '../../../../platform/ipc/common/mainProcessService.js';
import { IRequestOptions, URL_CONTENT_FETCHER_CHANNEL_NAME, UrlContentFetchChannelCommand } from '../../../../platform/request/common/UrlContentFetcherTypes.js';


export interface IUrlContentFetcherService {
	_serviceBrand: undefined;
	urlToMarkdown(url: string, options: IRequestOptions | undefined): Promise<string>;
}



export const IUrlContentFetcherService = createDecorator<IUrlContentFetcherService>('UrlContentFetcherService');


class UrlContentFetcherService extends Disposable implements IUrlContentFetcherService {
	_serviceBrand: undefined;
	private readonly channel: IChannel;
	constructor(
		@IMainProcessService readonly mainProcessService: IMainProcessService
	) {
		super();

		this.channel = mainProcessService.getChannel(URL_CONTENT_FETCHER_CHANNEL_NAME);

	}
	async urlToMarkdown(url: string, options: IRequestOptions | undefined): Promise<string> {
		const markdown: string = await this.channel.call(UrlContentFetchChannelCommand.URL_TO_MARKDOWN, [url, options]);
		return markdown;
	}

}

registerSingleton(IUrlContentFetcherService, UrlContentFetcherService, InstantiationType.Eager);
