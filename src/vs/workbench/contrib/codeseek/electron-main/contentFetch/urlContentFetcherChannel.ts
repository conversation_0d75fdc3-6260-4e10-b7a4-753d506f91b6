import { CancellationToken } from '../../../../../base/common/cancellation.js';
import { Event } from '../../../../../base/common/event.js';
import { IServerChannel } from '../../../../../base/parts/ipc/common/ipc.js';
import { UrlContentFetchChannelCommand } from '../../../../../platform/request/common/UrlContentFetcherTypes.js';
import { IHeadlessBrowserService } from '../../../../../platform/request/electron-main/UrlContentFetcher.js';


export class UrlContentFetcherChannel implements IServerChannel {
	constructor(private urlContentFetcher: IHeadlessBrowserService) { }
	call(ctx: string, command: string, arg?: any, cancellationToken?: CancellationToken): Promise<any> {
		switch (command) {
			case UrlContentFetchChannelCommand.URL_TO_MARKDOWN:
				return this.urlContentFetcher.urlToMarkdown(arg[0], arg[1]);

			default:
				throw new Error(`Unknown command: ${command}`);
		}
	}
	listen<T>(ctx: string, event: string, arg?: any): Event<T> {
		throw new Error('Method not implemented.');
	}

}
