import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { Disposable } from '../../../../base/common/lifecycle.js';
import { EnsureIndexParams, searchParams, QueryContextData, CodeBaseResponse, CreateRepoData, RepoSnapshotData, QueryContextParams, EventCodebaseOnProgressParams, State, DeleteRepoIndexParams, CreateRepoParams, CreateRepoConfig } from '../common/codebaseTypes.js';
import * as path from '../../../../base/common/path.js';
import { URI } from '../../../../base/common/uri.js';
import { IDirectoryNode, IFileNode, IProjectStructure } from '../common/codeseekFileService.js';
import { filenameToVscodeLanguage } from '../common/helpers/detectLanguage.js';
import * as crypto from 'crypto';
import { IFileService } from '../../../../platform/files/common/files.js';
import { ICodeseekLogger } from '../common/codeseekLogService.js';
import { ChildProcess, spawn } from 'child_process';
import { IProductService } from '../../../../platform/product/common/productService.js';
import { os } from '../browser/helpers/systemInfo.js';
import { RepoSnapshot } from '../common/codebaseTypes.js';


enum CodebaseUrl {
	createRepo = '/api/codebase/v1/createRepoIndex',
	queryRepoIndex = '/api/codebase/v1/queryRepoIndex',
	deleteRepoIndex = '/api/codebase/v1/deleteRepoIndex',
	queryRepoSnapshot = '/api/codebase/v1/repoSnapshot',
	createFileIndex = '/api/codebase/v1/createFileIndex',
	updateFileIndex = '/api/codebase/v1/updateFileIndex',
	deleteFileIndex = '/api/codebase/v1/deleteFileIndex',
	noticeRepoIndexCreateComplete = '/api/codebase/v1/repoIndexCreateComplete',
	queryContext = '/api/codebase/v1/queryContext',
	healthCheck = '/api/codebase/v1/heartbeat'
}

export type RemoteEnsureIndexParams = EnsureIndexParams & CreateRepoConfig & {
	onProgress: (p: EventCodebaseOnProgressParams) => void;
}

export interface ICodebaseRemoteMainService {
	readonly _serviceBrand: undefined;

	startCodebaseProcess(): Promise<boolean>;
	stopCodebaseProcess(): Promise<void>;
	isCodebaseRunning(): boolean;
	checkCodebaseHealth(): Promise<boolean>;

	ensureIndex(params: RemoteEnsureIndexParams): Promise<void>;
	getResults(params: searchParams): Promise<QueryContextData | undefined>;
	deleteRemoteIndex(params: DeleteRepoIndexParams): Promise<boolean>;
}

export const ICodebaseRemoteMainService = createDecorator<ICodebaseRemoteMainService>('codebaseRemoteMainService');
export class CodebaseRemoteMainService extends Disposable implements ICodebaseRemoteMainService {
	private readonly baseUrl = 'http://localhost:1323';
	readonly _serviceBrand: undefined;
	private allState: Record<string, State> = {};
	private readonly fileContentCache: Map<string, { content: string, hashcode: string, timestamp: number }> = new Map();
	private readonly CACHE_EXPIRY = 5 * 60 * 1000; // 5分钟缓存过期

	private codebaseProcess: ChildProcess | null = null;

	// 设置服务就绪检查超时时间
	private readonly SERVICE_READY_TIMEOUT = 10000; // 10秒

	constructor(
		@ICodeseekLogger private readonly logger: ICodeseekLogger,
		@IFileService private readonly fileService: IFileService,
		@IProductService private readonly productService: IProductService,
	) {
		super();
	}

	async getResults(params: searchParams): Promise<QueryContextData | undefined> {
		let repoId: string;
		if (Object.keys(this.allState).length === 0) {
			const repoIndexData = await this.getRepoId(params.repoUri);
			if (!repoIndexData) {
				this.logger.info(`repo not found, skip get results`);
				return undefined;
			}
			repoId = repoIndexData.repoId;
			if (repoIndexData.isComplete) {
				this.allState[params.repoUri.fsPath] = {
					repoId: repoIndexData.repoId,
					repoName: params.repoUri.path.split('/').pop()!,
					repoPath: params.repoUri.fsPath,
				};
			} else {
				return undefined;
			}
		} else {
			repoId = this.allState[params.repoUri.fsPath].repoId;
		}
		try {
			const requestData: QueryContextParams = {
				query: params.userQuery,
				repoId: repoId,
			};
			const queryContextData: QueryContextData = await this.post(CodebaseUrl.queryContext, requestData);
			return queryContextData;
		} catch (error) {
			this.logger.error(`Failed to get results for ${params.repoUri.fsPath} repo`);
			return undefined;
		}
	}

	private async getRepoId(repoUri: URI): Promise<CreateRepoData | null> {
		const body = {
			repoPath: repoUri.fsPath,
			repoName: repoUri.path.split('/').pop()
		};
		const response = await this.post(`${CodebaseUrl.queryRepoIndex}`, body, true, false);
		if (response.code === 0 || response.code === 2) {
			return response.data as CreateRepoData;
		}
		return null;
	}

	private async getFileContentAndHash(uri: URI, force = false): Promise<{ content: string, hashcode: string }> {
		const cacheKey = uri.path;
		const now = Date.now();
		const cached = this.fileContentCache.get(cacheKey);

		if (cached && (now - cached.timestamp) < this.CACHE_EXPIRY && !force) {
			return { content: cached.content, hashcode: cached.hashcode };
		}

		try {
			const uri_ = URI.parse(uri.path);
			const content = await this.fileService.readFile(uri_);
			const contentStr = content.value.toString();
			const hashcode = crypto.createHash('md5').update(contentStr).digest('hex');

			this.fileContentCache.set(cacheKey, {
				content: contentStr,
				hashcode,
				timestamp: now
			});

			return { content: contentStr, hashcode };
		} catch (error) {
			this.logger.error(`Failed to read file ${uri.path}:`, error);
			throw error;
		}
	}

	private clearExpiredCache(): void {
		const now = Date.now();
		for (const [key, value] of this.fileContentCache.entries()) {
			if (now - value.timestamp > this.CACHE_EXPIRY) {
				this.fileContentCache.delete(key);
			}
		}
	}

	async ensureIndex(params: RemoteEnsureIndexParams): Promise<void> {
		if (params.repoUri.scheme !== 'file') {
			throw new Error(`Invalid URI scheme: ${params.repoUri.scheme}. Expected 'file'.`);
		}

		try {
			const fileNodes = await this.collectFiles(params.repoUri, params.projectStructure!);
			if (fileNodes.length === 0) {
				this.logger.info(`the ${params.repoUri.fsPath} repo is empty`);
				return;
			}
			const totalFiles = fileNodes.length;

			const requestData: CreateRepoParams = {
				repoPath: params.repoUri.fsPath,
				repoName: params.repoUri.path.split('/').pop()!,
				config: {
					llm: params.llm,
				}
			};
			if (params.clangd) {
				requestData.config.clangd = params.clangd;
			}

			const repoData: CreateRepoData = await this.post(CodebaseUrl.createRepo, requestData);
			const repoSnapshotData: RepoSnapshotData = await this.get(`${CodebaseUrl.queryRepoSnapshot}/${repoData.repoId}`);
			const isNeedCreateIndex = await this.isNeedCreateIndex(params.repoUri, fileNodes, repoSnapshotData);
			if (!isNeedCreateIndex) {
				this.logger.info(`the ${params.repoUri.fsPath} repo already complete`);
				params.onProgress({ repoUri: params.repoUri, progress: 1, SyncedFileNumber: totalFiles });
				this.allState[params.repoUri.fsPath] = {
					repoId: repoData.repoId,
					repoName: requestData.repoName,
					repoPath: requestData.repoPath,
				};
				return;
			}
			this.logger.info(`${params.repoUri.fsPath} repo start create index`);
			delete this.allState[params.repoUri.fsPath];
			let indexedFiles = repoSnapshotData.repoSnapshot.length;
			let progress = indexedFiles / totalFiles;

			while (fileNodes.length > 0) {
				this.clearExpiredCache();
				const fileNode = fileNodes.shift();
				if (fileNode) {
					try {
						const relativePath = path.relative(params.repoUri.path, fileNode.uri.path);
						const { content, hashcode } = await this.getFileContentAndHash(fileNode.uri);
						const indexStatus = this.getIndexStatus(relativePath, hashcode, repoSnapshotData);
						const language = filenameToVscodeLanguage(fileNode.uri.path) ?? '';
						if (indexStatus == 'indexed') {
							continue;
						}
						const requestData = {
							repoId: repoData.repoId,
							relativePath,
							content,
							hashcode,
							language
						};

						if (indexStatus === 'updating') {
							await this.post(CodebaseUrl.updateFileIndex, requestData, false);
						} else {
							await this.post(CodebaseUrl.createFileIndex, requestData, false);
						}
						indexedFiles++;
					} catch (error) {
						this.logger.error(`Failed to process file ${fileNode.uri.path}:`, error);
					}
				}
				progress = progress >= 0.99 ? 0.99 : indexedFiles / totalFiles;
				params.onProgress({ repoUri: params.repoUri, progress: progress, SyncedFileNumber: indexedFiles });
			}

			for (const repoSnapshot of repoSnapshotData.repoSnapshot) {
				if (!fileNodes.find(fileNode => path.relative(params.repoUri.path, fileNode.uri.path) === repoSnapshot.relativePath)) {
					const data = { repoId: repoData.repoId, relativePath: repoSnapshot.relativePath };
					await this.post(CodebaseUrl.deleteFileIndex, data, false);
				}
			}

			params.onProgress({ repoUri: params.repoUri, progress: 1, SyncedFileNumber: totalFiles });
			this.logger.info(`the ${params.repoUri.fsPath} repo index create complete`);
			await this.post(CodebaseUrl.noticeRepoIndexCreateComplete, { repoId: repoData.repoId });
			this.allState[params.repoUri.fsPath] = {
				repoId: repoData.repoId,
				repoName: requestData.repoName,
				repoPath: requestData.repoPath,
			};
		} catch (error) {
			this.logger.error(`Failed to ensure index for ${params.repoUri.fsPath} repo`);
			throw error;
		}
	}

	async deleteRemoteIndex(params: DeleteRepoIndexParams): Promise<boolean> {
		try {
			const repoIndexData = await this.getRepoId(params.repoUri);
			if (!repoIndexData) {
				this.logger.error(`repo not found, skip delete remote index`);
				return true;
			}
			const repoId = repoIndexData.repoId;
			await this.post(CodebaseUrl.deleteRepoIndex, { repoId });
			delete this.allState[params.repoUri.fsPath];
			return true;
		} catch (error) {
			this.logger.error(`Failed to delete remote index for ${params.repoUri.fsPath} repo`);
			return false;
		}
	}

	private async collectFiles(repoUri: URI, projectStructure: IProjectStructure): Promise<IFileNode[]> {
		const fileNodes: IFileNode[] = [];
		const MAX_FILES = 100000;
		try {
			if (!projectStructure.root) {
				return fileNodes;
			}

			const stack: Array<IDirectoryNode | IFileNode> = [projectStructure.root];
			const processedPaths = new Set<string>();

			while (stack.length > 0 && fileNodes.length < MAX_FILES) {
				const currentNode = stack.pop();

				if (!currentNode) {
					continue;
				}

				const nodePath = currentNode.uri.path;
				if (processedPaths.has(nodePath)) {
					continue;
				}
				processedPaths.add(nodePath);

				if (currentNode.type === 'file') {
					fileNodes.push(currentNode as IFileNode);
				} else if (currentNode.type === 'directory') {
					const dirNode = currentNode as IDirectoryNode;
					if (dirNode.children && dirNode.children.length > 0) {
						for (let i = dirNode.children.length - 1; i >= 0; i--) {
							stack.push(dirNode.children[i]);
						}
					}
				}
			}

			if (fileNodes.length >= MAX_FILES) {
				this.logger.warn(`File collection limited to ${MAX_FILES} files to prevent memory issues`);
			}
		} catch (error) {
			this.logger.error('the error when collect files:', error);
		}
		return fileNodes;
	}

	private getIndexStatus(relativePath: string, hashcode: string, repoSnapshotData: RepoSnapshotData): 'unindexed' | 'indexed' | 'updating' {
		if (repoSnapshotData.repoSnapshot.find((snapshot: RepoSnapshot) => snapshot.relativePath === relativePath && snapshot.hashcode === hashcode)) {
			return 'indexed';
		} else if (repoSnapshotData.repoSnapshot.find((snapshot: RepoSnapshot) => snapshot.relativePath === relativePath)) {
			return 'updating';
		}
		return 'unindexed';
	}

	private async isNeedCreateIndex(repoUri: URI, fileNodes: IFileNode[], repoSnapshotData: RepoSnapshotData): Promise<boolean> {
		for (const fileNode of fileNodes) {
			const relativePath = path.relative(repoUri.path, fileNode.uri.path);
			const { hashcode } = await this.getFileContentAndHash(fileNode.uri, true);
			if (this.getIndexStatus(relativePath, hashcode, repoSnapshotData) === 'unindexed') {
				return true;
			}
		}
		for (const repoSnapshot of repoSnapshotData.repoSnapshot) {
			if (fileNodes.find(fileNode => path.relative(repoUri.path, fileNode.uri.path) === repoSnapshot.relativePath)) {
				continue;
			}
			return true;
		}
		return false;
	}

	private async post(url: string, data: any, isLog: boolean = true, isThrow: boolean = true): Promise<any> {
		try {
			const url_ = `${this.baseUrl}${url}`;
			if (isLog) {
				this.logger.info(`request url: ${url_}, data: ${JSON.stringify(data)}`);
			}
			const response = await fetch(url_, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(data)
			});

			if (!response.ok) {
				this.logger.error(`HTTP error! status: ${response.status}`);
				throw new Error(`HTTP error! status: ${response.status}`);
			}

			const responseJson = await response.json() satisfies CodeBaseResponse;
			if (isLog) {
				this.logger.info(`response: ${JSON.stringify(responseJson)}`);
			}
			if (isThrow) {
				if (responseJson.code === 0 || responseJson.code === 2) {
					return responseJson.data;
				}
				throw new Error(responseJson.message);
			}
			return responseJson;
		} catch (error) {
			this.logger.error(error);
			throw error;
		}
	}

	private async get(url: string, isLog: boolean = true): Promise<any> {
		const url_ = `${this.baseUrl}${url}`;
		try {
			this.logger.info(`request url: ${url_}`);
			const response = await fetch(url_, {
				method: 'GET',
				headers: {
					'Content-Type': 'application/json'
				}
			});
			if (!response.ok) {
				this.logger.error(`HTTP error! status: ${response.status}`);
				throw new Error(`HTTP error! status: ${response.status}`);
			}
			const responseJson = await response.json() satisfies CodeBaseResponse;
			this.logger.info(`response: ${JSON.stringify(responseJson)}`);
			if (responseJson.code === 0 || responseJson.code === 2) {
				return responseJson.data;
			}
			throw new Error(responseJson.message);
		} catch (error) {
			if (isLog) {
				this.logger.error(error);
			}
			throw error;
		}
	}

	async checkCodebaseHealth(): Promise<boolean> {
		try {
			if (!this.codebaseProcess) {
				this.logger.info('Codebase process is not running');
				return false;
			}

			this.logger.info('Checking codebase health using heartbeat endpoint');
			const healthCheckData = await this.get(CodebaseUrl.healthCheck, false);

			if (healthCheckData === null) {
				this.logger.error('Health check failed, service is not responding');
				return false;
			}

			this.logger.info('Health check succeeded, service is healthy');
			return true;
		} catch (error) {
			this.logger.error(`Health check failed with error: ${error.message}`);
			return false;
		}
	}

	async startCodebaseProcess(): Promise<boolean> {
		if (this.codebaseProcess) {
			this.logger.info('Codebase process is already running');
			const isHealthy = await this.checkCodebaseHealth();
			if (!isHealthy) {
				this.logger.warn('Codebase process is running but health check failed, restarting service');
				await this.stopCodebaseProcess();
			} else {
				return true;
			}
		}

		try {
			const dataFolderName = this.productService.dataFolderName;
			const codebasePath = await this.getCodebasePath(dataFolderName);
			this.logger.info(`codebasePath: ${codebasePath}`);

			if (!codebasePath) {
				throw new Error('Codebase executable not found');
			}

			this.logger.info(`Starting codebase process from ${codebasePath}`);

			this.codebaseProcess = spawn(codebasePath, [], {
				stdio: ['ignore', 'pipe', 'pipe'],
				detached: false
			});

			this.codebaseProcess.stderr?.on('data', (data) => {
				this.logger.error(`Codebase stderr: ${data.toString()}`);
			});

			this.codebaseProcess.on('exit', (code, signal) => {
				this.logger.info(`Codebase process exited with code ${code}, signal: ${signal}`);
				this.codebaseProcess = null;
			});

			this.codebaseProcess.on('error', (err) => {
				this.logger.error(`Codebase process error: ${err.message}`);
				this.codebaseProcess = null;
			});

			let retries = 0;
			const maxRetries = 10;
			const retryInterval = this.SERVICE_READY_TIMEOUT / maxRetries;

			while (retries < maxRetries) {
				this.logger.info(`Waiting for codebase service to be ready, attempt ${retries + 1}/${maxRetries}`);
				await new Promise(resolve => setTimeout(resolve, retryInterval));
				const isHealthy = await this.checkCodebaseHealth();
				if (isHealthy) {
					this.logger.info('Codebase process started successfully and service is ready');
					return true;
				}
				retries++;
			}

			this.logger.error('Codebase service failed to become ready within timeout');
			if (this.codebaseProcess) {
				this.codebaseProcess.kill();
				this.codebaseProcess = null;
			}
			return false;
		} catch (error) {
			this.logger.error(`Failed to start codebase process: ${error.message}`);
			throw error;
		}
	}

	async stopCodebaseProcess(): Promise<void> {
		if (!this.codebaseProcess) {
			this.logger.info('No codebase process to stop');
			return;
		}

		return new Promise<void>((resolve, reject) => {
			try {
				this.logger.info('Stopping codebase process...');
				if (!this.codebaseProcess) {
					this.logger.info('No codebase process to stop');
					return
				}

				const timeout = setTimeout(() => {
					if (this.codebaseProcess) {
						this.logger.warn('Killing codebase process forcefully');
						this.codebaseProcess.kill('SIGKILL');
					}
				}, 5000);

				this.codebaseProcess.on('exit', () => {
					clearTimeout(timeout);
					this.codebaseProcess = null;
					this.logger.info('Codebase process stopped successfully');
					resolve();
				});

				this.codebaseProcess.kill('SIGTERM');
			} catch (error) {
				this.logger.error(`Error stopping codebase process: ${error.message}`);
				reject(error);
			}
		});
	}

	isCodebaseRunning(): boolean {
		return this.codebaseProcess !== null;
	}

	private async getCodebasePath(dataFolderName: string): Promise<string | null> {
		let codebaseDir;
		if (os === 'windows') {
			codebaseDir = path.join(process.execPath, '..', 'tools', 'codebase');
		} else {
			codebaseDir = path.join(dataFolderName, 'tools', 'codebase');
		}
		let execName = 'codebase';
		if (os === 'windows') {
			execName = 'codebase.exe';
		}
		const codebasePath = path.join(codebaseDir, execName);
		if (await this.fileService.exists(URI.file(codebasePath))) {
			return codebasePath;
		}
		return null;
	}
}

registerSingleton(ICodebaseRemoteMainService, CodebaseRemoteMainService, InstantiationType.Eager);

