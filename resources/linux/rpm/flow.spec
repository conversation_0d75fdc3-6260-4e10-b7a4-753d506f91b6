%global debug_package %{nil}
%global _use_internal_dependency_generator 0
AutoReqProv: no
%define _build_id_links none

Name:           flow
Version:        -
Release:        1%{?dist}
Summary:        Flow (Portable Version)
License:        MIT
Group:          Development/Tools
Vendor:         ZTE
Source0:        flow-linux-x64.tar.gz
Source1:        flow.desktop
Source2:        flow.png
Source3:        symf-x86_64-linux-musl
Source4:        argv.json
Source5:        ctags
Source6:        clangd
BuildArch:      x86_64
Requires:       gtk3, libX11, libXtst, libxkbfile, libsecret

%description
This is a portable version of Flow for Linux.

%prep
%setup -c -n flow-linux-x64

%build
# No compilation needed for this package

%install
mkdir -p %{buildroot}/opt/flow
cp -r * %{buildroot}/opt/flow/

# 创建启动包装脚本
mkdir -p %{buildroot}/usr/bin
cat > %{buildroot}/usr/bin/flow << 'EOF'
#!/bin/bash
# 检查并设置 symf-x86_64-linux-musl
if [ ! -f "$HOME/.flow/symf/bin/symf-x86_64-linux-musl" ]; then
    mkdir -p "$HOME/.flow/symf/bin"
    sudo cp /opt/flow/bin/symf-x86_64-linux-musl "$HOME/.flow/symf/bin/"
    sudo chmod +x "$HOME/.flow/symf/bin/symf-x86_64-linux-musl"
fi

# 检查并设置 ctags
if [ ! -f "$HOME/.flow/ctags/bin/ctags" ]; then
    mkdir -p "$HOME/.flow/ctags/bin"
    sudo cp /opt/flow/bin/ctags/ctags "$HOME/.flow/ctags/bin/"
    sudo chmod +x "$HOME/.flow/ctags/bin/ctags"
fi

# 检查并设置 clangd
if [ ! -f "$HOME/.flow/clangd/bin/clangd" ]; then
    mkdir -p "$HOME/.flow/clangd/bin"
    sudo cp /opt/flow/bin/clangd/clangd "$HOME/.flow/clangd/bin/"
    sudo chmod +x "$HOME/.flow/clangd/bin/clangd"
fi

mkdir -p "$HOME/.flow"
# 设置 argv.json
sudo cp /opt/flow/bin/argv.json "$HOME/.flow/"
sudo chown $(id -u):$(id -g) "$HOME/.flow/argv.json"

EXTENSION_DIR="/opt/flow/resources/app/extensions/icode-team.WX-AI-Assistant"
if [ -d "$EXTENSION_DIR" ]; then
    if command -v sudo >/dev/null 2>&1; then
        sudo chown -R $(id -u):$(id -g) "$EXTENSION_DIR" 2>/dev/null || true
    else
        chown -R $(id -u):$(id -g) "$EXTENSION_DIR" 2>/dev/null || true
    fi
fi

# 启动主程序
exec /opt/flow/bin/flow --password-store=basic "$@"
EOF
chmod +x %{buildroot}/usr/bin/flow

# 拷贝 desktop 文件到应用程序目录
mkdir -p %{buildroot}/%{_datadir}/applications
cp %{SOURCE1} %{buildroot}/%{_datadir}/applications/

# 拷贝图标文件到标准图标目录
mkdir -p %{buildroot}/%{_datadir}/icons/hicolor/{16,32,48,64,128,256}x{16,32,48,64,128,256}/apps
cp %{SOURCE2} %{buildroot}/%{_datadir}/icons/hicolor/16x16/apps/
cp %{SOURCE2} %{buildroot}/%{_datadir}/icons/hicolor/32x32/apps/
cp %{SOURCE2} %{buildroot}/%{_datadir}/icons/hicolor/48x48/apps/
cp %{SOURCE2} %{buildroot}/%{_datadir}/icons/hicolor/64x64/apps/
cp %{SOURCE2} %{buildroot}/%{_datadir}/icons/hicolor/128x128/apps/
cp %{SOURCE2} %{buildroot}/%{_datadir}/icons/hicolor/256x256/apps/

# 设置正确的文件权限
chmod 644 %{buildroot}/%{_datadir}/applications/flow.desktop
chmod 644 %{buildroot}/%{_datadir}/icons/hicolor/*/apps/flow.png

# 拷贝symf-x86_64-linux-musl 到 opt/flow/bin 目录
mkdir -p %{buildroot}/opt/flow/bin
cp %{SOURCE3} %{buildroot}/opt/flow/bin/

# 拷贝 argv.json 到 opt/flow/bin 目录
mkdir -p %{buildroot}/opt/flow/bin
cp %{SOURCE4} %{buildroot}/opt/flow/bin/

# 拷贝 ctags 到 opt/flow/bin 目录
mkdir -p %{buildroot}/opt/flow/bin/ctags
cp %{SOURCE5} %{buildroot}/opt/flow/bin/ctags

# 拷贝 clangd 到 opt/flow/bin 目录
mkdir -p %{buildroot}/opt/flow/bin/clangd
cp %{SOURCE6} %{buildroot}/opt/flow/bin/clangd

%files
/opt/flow
%attr(755,root,root) /usr/bin/flow
%attr(644,root,root) %{_datadir}/applications/flow.desktop
%attr(644,root,root) %{_datadir}/icons/hicolor/16x16/apps/flow.png
%attr(644,root,root) %{_datadir}/icons/hicolor/32x32/apps/flow.png
%attr(644,root,root) %{_datadir}/icons/hicolor/48x48/apps/flow.png
%attr(644,root,root) %{_datadir}/icons/hicolor/64x64/apps/flow.png
%attr(644,root,root) %{_datadir}/icons/hicolor/128x128/apps/flow.png
%attr(644,root,root) %{_datadir}/icons/hicolor/256x256/apps/flow.png

%post
# 更新桌面数据库
if [ -x /usr/bin/update-desktop-database ]; then
    /usr/bin/update-desktop-database %{_datadir}/applications &> /dev/null || :
fi

# 更新图标缓存
if [ -x /usr/bin/gtk-update-icon-cache ]; then
    /usr/bin/gtk-update-icon-cache --quiet %{_datadir}/icons/hicolor &> /dev/null || :
fi

%postun
# 卸载后更新桌面数据库
if [ $1 -eq 0 ] ; then
    if [ -x /usr/bin/update-desktop-database ]; then
        /usr/bin/update-desktop-database %{_datadir}/applications &> /dev/null || :
    fi

    # 卸载后更新图标缓存
    if [ -x /usr/bin/gtk-update-icon-cache ]; then
        /usr/bin/gtk-update-icon-cache --quiet %{_datadir}/icons/hicolor &> /dev/null || :
    fi
fi

%changelog
* Mon Mar 10 2025 <NAME_EMAIL> - 0.1.10
- Initial RPM package

%_missing_build_ids_terminate_build 0
